<?php
require_once '../config/config.php';
requireAuth();

$page_title = 'নতুন বিক্রয়';

$db = new Database();
$conn = $db->getConnection();

$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $customer_id = !empty($_POST['customer_id']) ? (int)$_POST['customer_id'] : null;
    $sale_date = sanitize($_POST['sale_date']);
    $products = $_POST['products'] ?? [];
    $discount = (float)($_POST['discount'] ?? 0);
    $paid_amount = (float)($_POST['paid_amount'] ?? 0);
    $notes = sanitize($_POST['notes']);

    // Validation
    if (empty($sale_date)) {
        $errors[] = 'বিক্রয়ের তারিখ প্রয়োজন।';
    }

    if (empty($products)) {
        $errors[] = 'অন্তত একটি পণ্য নির্বাচন করুন।';
    }

    $total_amount = 0;
    foreach ($products as $product) {
        if (!empty($product['product_id']) && !empty($product['quantity']) && !empty($product['unit_price'])) {
            $total_amount += $product['quantity'] * $product['unit_price'];
        }
    }

    if ($total_amount <= 0) {
        $errors[] = 'মোট পরিমাণ ০ এর চেয়ে বেশি হতে হবে।';
    }

    if (empty($errors)) {
        try {
            $conn->beginTransaction();

            // Generate invoice number
            $invoice_no = generateInvoiceNumber('INV');

            // Calculate amounts
            $final_amount = $total_amount - $discount;
            $due_amount = $final_amount - $paid_amount;

            // Insert sale
            $query = "INSERT INTO sales (customer_id, invoice_no, sale_date, total_amount, paid_amount, 
                      due_amount, discount, notes, created_by) 
                      VALUES (:customer_id, :invoice_no, :sale_date, :total_amount, :paid_amount, 
                      :due_amount, :discount, :notes, :created_by)";
            
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->bindParam(':invoice_no', $invoice_no);
            $stmt->bindParam(':sale_date', $sale_date);
            $stmt->bindParam(':total_amount', $final_amount);
            $stmt->bindParam(':paid_amount', $paid_amount);
            $stmt->bindParam(':due_amount', $due_amount);
            $stmt->bindParam(':discount', $discount);
            $stmt->bindParam(':notes', $notes);
            $stmt->bindParam(':created_by', $_SESSION['user_id']);
            
            $stmt->execute();
            $sale_id = $conn->lastInsertId();

            // Insert sale details and update stock
            foreach ($products as $product) {
                if (!empty($product['product_id']) && !empty($product['quantity']) && !empty($product['unit_price'])) {
                    $product_id = (int)$product['product_id'];
                    $quantity = (int)$product['quantity'];
                    $unit_price = (float)$product['unit_price'];
                    $total_price = $quantity * $unit_price;

                    // Insert sale detail
                    $query = "INSERT INTO sale_details (sale_id, product_id, quantity, unit_price, total_price) 
                              VALUES (:sale_id, :product_id, :quantity, :unit_price, :total_price)";
                    $stmt = $conn->prepare($query);
                    $stmt->bindParam(':sale_id', $sale_id);
                    $stmt->bindParam(':product_id', $product_id);
                    $stmt->bindParam(':quantity', $quantity);
                    $stmt->bindParam(':unit_price', $unit_price);
                    $stmt->bindParam(':total_price', $total_price);
                    $stmt->execute();

                    // Update product stock
                    updateProductStock($product_id, $quantity, 'out', 'sale', $sale_id);
                }
            }

            // Update customer balance if customer selected
            if ($customer_id && $due_amount > 0) {
                $query = "UPDATE customers SET balance = balance + :due_amount WHERE id = :customer_id";
                $stmt = $conn->prepare($query);
                $stmt->bindParam(':due_amount', $due_amount);
                $stmt->bindParam(':customer_id', $customer_id);
                $stmt->execute();
            }

            // Record payment if paid amount > 0
            if ($paid_amount > 0) {
                $query = "INSERT INTO payments (type, reference_id, amount, payment_date, created_by) 
                          VALUES ('sale', :reference_id, :amount, :payment_date, :created_by)";
                $stmt = $conn->prepare($query);
                $stmt->bindParam(':reference_id', $sale_id);
                $stmt->bindParam(':amount', $paid_amount);
                $stmt->bindParam(':payment_date', $sale_date);
                $stmt->bindParam(':created_by', $_SESSION['user_id']);
                $stmt->execute();
            }

            $conn->commit();
            
            logActivity('Sale Created', "Created sale: $invoice_no");
            showAlert('বিক্রয় সফলভাবে সম্পন্ন হয়েছে।', 'success');
            
            // Redirect to invoice
            redirect("invoice.php?id=$sale_id");
            
        } catch (Exception $e) {
            $conn->rollback();
            $errors[] = 'বিক্রয় সংরক্ষণ করতে সমস্যা হয়েছে: ' . $e->getMessage();
        }
    }
}

include '../includes/header.php';
?>

<div class="main-content">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-shopping-cart"></i> নতুন বিক্রয়</h2>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-list"></i> বিক্রয় তালিকা
            </a>
        </div>

        <!-- Error Messages -->
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- Sale Form -->
        <form method="POST" action="" id="sale-form">
            <div class="row">
                <!-- Customer & Date Info -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>কাস্টমার তথ্য</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="customer_search" class="form-label">কাস্টমার</label>
                                <input type="text" id="customer_search" class="form-control customer-search" 
                                       placeholder="কাস্টমার খুঁজুন (ঐচ্ছিক)">
                                <input type="hidden" id="customer_id" name="customer_id">
                            </div>
                            
                            <div class="form-group">
                                <label for="sale_date" class="form-label">বিক্রয়ের তারিখ *</label>
                                <input type="date" id="sale_date" name="sale_date" class="form-control required" 
                                       value="<?php echo date('Y-m-d'); ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Summary -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>বিক্রয় সারসংক্ষেপ</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <strong>সাবটোটাল:</strong>
                                </div>
                                <div class="col-6 text-right">
                                    <span id="subtotal">৳ 0.00</span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <strong>ছাড়:</strong>
                                </div>
                                <div class="col-6">
                                    <input type="number" name="discount" id="discount" class="form-control form-control-sm text-right" 
                                           step="0.01" min="0" value="0" style="display: inline-block; width: 80px;">
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-6">
                                    <strong>মোট:</strong>
                                </div>
                                <div class="col-6 text-right">
                                    <strong><span id="total">৳ 0.00</span></strong>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <strong>প্রদত্ত:</strong>
                                </div>
                                <div class="col-6">
                                    <input type="number" name="paid_amount" id="paid_amount" class="form-control form-control-sm text-right" 
                                           step="0.01" min="0" value="0" style="display: inline-block; width: 80px;">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <strong>বকেয়া:</strong>
                                </div>
                                <div class="col-6 text-right">
                                    <strong><span id="due">৳ 0.00</span></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>পণ্য তালিকা</h5>
                    <button type="button" class="btn btn-sm btn-primary float-right" onclick="addProductRow()">
                        <i class="fas fa-plus"></i> পণ্য যোগ করুন
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table" id="products-table">
                            <thead>
                                <tr>
                                    <th width="40%">পণ্য</th>
                                    <th width="15%">পরিমাণ</th>
                                    <th width="15%">একক মূল্য</th>
                                    <th width="15%">মোট</th>
                                    <th width="10%">কার্যক্রম</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <input type="text" class="form-control product-search" placeholder="পণ্য খুঁজুন...">
                                        <input type="hidden" name="products[0][product_id]" class="product-id">
                                    </td>
                                    <td>
                                        <input type="number" name="products[0][quantity]" class="form-control quantity" 
                                               min="1" value="1" onchange="calculateRowTotal(this)">
                                    </td>
                                    <td>
                                        <input type="number" name="products[0][unit_price]" class="form-control unit-price" 
                                               step="0.01" min="0" onchange="calculateRowTotal(this)">
                                    </td>
                                    <td>
                                        <input type="number" class="form-control row-total" readonly>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeProductRow(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Notes -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="form-group">
                        <label for="notes" class="form-label">মন্তব্য</label>
                        <textarea id="notes" name="notes" class="form-control" rows="2" 
                                  placeholder="অতিরিক্ত মন্তব্য (ঐচ্ছিক)"></textarea>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save"></i> বিক্রয় সম্পন্ন করুন
                </button>
                <a href="index.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times"></i> বাতিল
                </a>
            </div>
        </form>
    </div>
</div>

<script>
let productRowIndex = 1;

$(document).ready(function() {
    // Initialize autocomplete
    initProductSearch();
    initCustomerSearch();
    
    // Calculate totals on input change
    $(document).on('input', '#discount, #paid_amount', function() {
        calculateTotals();
    });
});

function addProductRow() {
    let newRow = `
        <tr>
            <td>
                <input type="text" class="form-control product-search" placeholder="পণ্য খুঁজুন...">
                <input type="hidden" name="products[${productRowIndex}][product_id]" class="product-id">
            </td>
            <td>
                <input type="number" name="products[${productRowIndex}][quantity]" class="form-control quantity" 
                       min="1" value="1" onchange="calculateRowTotal(this)">
            </td>
            <td>
                <input type="number" name="products[${productRowIndex}][unit_price]" class="form-control unit-price" 
                       step="0.01" min="0" onchange="calculateRowTotal(this)">
            </td>
            <td>
                <input type="number" class="form-control row-total" readonly>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeProductRow(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
    
    $('#products-table tbody').append(newRow);
    productRowIndex++;
    
    // Re-initialize autocomplete for new row
    initProductSearch();
}

function removeProductRow(button) {
    let rowCount = $('#products-table tbody tr').length;
    if (rowCount > 1) {
        $(button).closest('tr').remove();
        calculateTotals();
    } else {
        alert('অন্তত একটি পণ্য থাকতে হবে।');
    }
}

function calculateRowTotal(element) {
    let row = $(element).closest('tr');
    let quantity = parseFloat(row.find('.quantity').val()) || 0;
    let unitPrice = parseFloat(row.find('.unit-price').val()) || 0;
    let total = quantity * unitPrice;
    
    row.find('.row-total').val(total.toFixed(2));
    calculateTotals();
}

function calculateTotals() {
    let subtotal = 0;
    
    $('.row-total').each(function() {
        subtotal += parseFloat($(this).val()) || 0;
    });
    
    let discount = parseFloat($('#discount').val()) || 0;
    let total = subtotal - discount;
    let paid = parseFloat($('#paid_amount').val()) || 0;
    let due = total - paid;
    
    $('#subtotal').text('৳ ' + subtotal.toFixed(2));
    $('#total').text('৳ ' + total.toFixed(2));
    $('#due').text('৳ ' + due.toFixed(2));
}
</script>

<?php include '../includes/footer.php'; ?>
