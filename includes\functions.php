<?php
// সাধারণ ফাংশন
// Common Functions

// Sanitize input data
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Format currency
function formatCurrency($amount) {
    return CURRENCY . ' ' . number_format($amount, 2);
}

// Format date
function formatDate($date, $format = 'd/m/Y') {
    return date($format, strtotime($date));
}

// Format datetime
function formatDateTime($datetime, $format = 'd/m/Y H:i') {
    return date($format, strtotime($datetime));
}

// Generate invoice number
function generateInvoiceNumber($prefix = 'INV') {
    return $prefix . date('Ymd') . rand(1000, 9999);
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// Get current user info
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    $db = new Database();
    $conn = $db->getConnection();
    
    $query = "SELECT * FROM users WHERE id = :user_id AND status = 'active'";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':user_id', $_SESSION['user_id']);
    $stmt->execute();
    
    return $stmt->fetch();
}

// Check user permission
function hasPermission($required_role) {
    $user = getCurrentUser();
    if (!$user) return false;
    
    $roles = ['staff' => 1, 'manager' => 2, 'admin' => 3];
    $user_level = $roles[$user['role']] ?? 0;
    $required_level = $roles[$required_role] ?? 0;
    
    return $user_level >= $required_level;
}

// Redirect function
function redirect($url) {
    header("Location: " . $url);
    exit();
}

// Show alert message
function showAlert($message, $type = 'info') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

// Get and clear alert message
function getAlert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        unset($_SESSION['alert']);
        return $alert;
    }
    return null;
}

// Convert English numbers to Bengali
function toBengaliNumber($number) {
    $bengali_digits = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
    $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    return str_replace($english_digits, $bengali_digits, $number);
}

// Convert Bengali numbers to English
function toEnglishNumber($number) {
    $bengali_digits = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
    $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    return str_replace($bengali_digits, $english_digits, $number);
}

// Get low stock products
function getLowStockProducts() {
    $db = new Database();
    $conn = $db->getConnection();
    
    $query = "SELECT * FROM products WHERE stock_quantity <= min_stock_level AND status = 'active'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    
    return $stmt->fetchAll();
}

// Get expired products
function getExpiredProducts() {
    $db = new Database();
    $conn = $db->getConnection();
    
    $query = "SELECT * FROM products WHERE expiry_date <= CURDATE() AND status = 'active'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    
    return $stmt->fetchAll();
}

// Get products expiring soon (within 30 days)
function getExpiringSoonProducts($days = 30) {
    $db = new Database();
    $conn = $db->getConnection();
    
    $query = "SELECT * FROM products WHERE expiry_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL :days DAY) AND status = 'active'";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':days', $days);
    $stmt->execute();
    
    return $stmt->fetchAll();
}

// Update product stock
function updateProductStock($product_id, $quantity, $movement_type, $reference_type, $reference_id = null) {
    $db = new Database();
    $conn = $db->getConnection();
    
    try {
        $conn->beginTransaction();
        
        // Update product stock
        if ($movement_type == 'in') {
            $query = "UPDATE products SET stock_quantity = stock_quantity + :quantity WHERE id = :product_id";
        } else {
            $query = "UPDATE products SET stock_quantity = stock_quantity - :quantity WHERE id = :product_id";
        }
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':quantity', $quantity);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();
        
        // Record stock movement
        $query = "INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id) 
                  VALUES (:product_id, :movement_type, :quantity, :reference_type, :reference_id)";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->bindParam(':movement_type', $movement_type);
        $stmt->bindParam(':quantity', $quantity);
        $stmt->bindParam(':reference_type', $reference_type);
        $stmt->bindParam(':reference_id', $reference_id);
        $stmt->execute();
        
        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        return false;
    }
}

// Log activity
function logActivity($action, $details = '') {
    $db = new Database();
    $conn = $db->getConnection();
    
    $user_id = $_SESSION['user_id'] ?? null;
    
    $query = "INSERT INTO activity_logs (user_id, action, details, ip_address) VALUES (:user_id, :action, :details, :ip_address)";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->bindParam(':action', $action);
    $stmt->bindParam(':details', $details);
    $stmt->bindParam(':ip_address', $_SERVER['REMOTE_ADDR']);
    $stmt->execute();
}
?>
