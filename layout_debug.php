<?php
// Layout Debug Tool
require_once 'config/config.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$page_title = 'লেআউট ডিবাগ টুল';

include 'includes/header.php';
?>

<div class="dashboard-container">
    <div class="container-fluid">
        <div class="dashboard-header">
            <h1 class="dashboard-title">
                <i class="fas fa-bug"></i>
                লেআউট ডিবাগ টুল
            </h1>
            <p class="dashboard-subtitle">
                ডিজাইন সমস্যা চিহ্নিতকরণ এবং সমাধান
            </p>
        </div>

        <!-- Debug Controls -->
        <div class="dashboard-card">
            <div class="card-header-modern">
                <h3 class="card-title-modern">
                    <i class="fas fa-tools"></i>
                    ডিবাগ কন্ট্রোল
                </h3>
            </div>
            <div style="padding: 20px;">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-primary" onclick="toggleDebugBorders()">
                        <i class="fas fa-border-all"></i> বর্ডার দেখান/লুকান
                    </button>
                    <button type="button" class="btn btn-success" onclick="checkOverflow()">
                        <i class="fas fa-search"></i> Overflow চেক করুন
                    </button>
                    <button type="button" class="btn btn-warning" onclick="measureElements()">
                        <i class="fas fa-ruler"></i> এলিমেন্ট মাপুন
                    </button>
                    <button type="button" class="btn btn-info" onclick="showViewportInfo()">
                        <i class="fas fa-desktop"></i> ভিউপোর্ট তথ্য
                    </button>
                    <button type="button" class="btn btn-danger" onclick="resetDebug()">
                        <i class="fas fa-undo"></i> রিসেট
                    </button>
                </div>
            </div>
        </div>

        <!-- Debug Results -->
        <div id="debug-results" class="dashboard-card" style="display: none;">
            <div class="card-header-modern">
                <h3 class="card-title-modern">
                    <i class="fas fa-clipboard-list"></i>
                    ডিবাগ ফলাফল
                </h3>
            </div>
            <div id="debug-output" style="padding: 20px;">
                <!-- Results will be shown here -->
            </div>
        </div>

        <!-- Test Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="stat-number">১২৩</div>
                <div class="stat-label">টেস্ট কার্ড ১</div>
            </div>
            
            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-number">৪৫৬</div>
                <div class="stat-label">টেস্ট কার্ড ২</div>
            </div>
            
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number">৭৮৯</div>
                <div class="stat-label">টেস্ট কার্ড ৩</div>
            </div>
            
            <div class="stat-card danger">
                <div class="stat-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-number">১০১১</div>
                <div class="stat-label">টেস্ট কার্ড ৪</div>
            </div>
        </div>

        <!-- Test Quick Actions -->
        <div class="dashboard-card">
            <div class="card-header-modern">
                <h3 class="card-title-modern">
                    <i class="fas fa-bolt"></i>
                    টেস্ট কুইক অ্যাকশন
                </h3>
            </div>
            <div class="quick-actions-grid">
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="quick-action-title">অ্যাকশন ১</div>
                    <div class="quick-action-desc">টেস্ট বিবরণ</div>
                </div>
                
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="quick-action-title">অ্যাকশন ২</div>
                    <div class="quick-action-desc">টেস্ট বিবরণ</div>
                </div>
                
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-trash"></i>
                    </div>
                    <div class="quick-action-title">অ্যাকশন ৩</div>
                    <div class="quick-action-desc">টেস্ট বিবরণ</div>
                </div>
            </div>
        </div>

        <!-- Test Row/Col -->
        <div class="row">
            <div class="col-md-8">
                <div class="dashboard-card">
                    <div class="card-header-modern">
                        <h3 class="card-title-modern">
                            <i class="fas fa-columns"></i>
                            কলাম ৮ (৬৬.৬৭%)
                        </h3>
                    </div>
                    <div style="padding: 20px;">
                        <p>এই কলামটি ৮/১২ = ৬৬.৬৭% প্রস্থ নেওয়ার কথা।</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="dashboard-card">
                    <div class="card-header-modern">
                        <h3 class="card-title-modern">
                            <i class="fas fa-columns"></i>
                            কলাম ৪ (৩৩.৩৩%)
                        </h3>
                    </div>
                    <div style="padding: 20px;">
                        <p>এই কলামটি ৪/১২ = ৩৩.৩৩% প্রস্থ নেওয়ার কথা।</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let debugMode = false;

function toggleDebugBorders() {
    debugMode = !debugMode;
    
    if (debugMode) {
        $('*').css({
            'border': '1px solid red',
            'box-sizing': 'border-box'
        });
        showNotification('ডিবাগ বর্ডার চালু করা হয়েছে', 'info');
    } else {
        $('*').css('border', '');
        showNotification('ডিবাগ বর্ডার বন্ধ করা হয়েছে', 'info');
    }
}

function checkOverflow() {
    const results = [];
    const elements = ['body', '.dashboard-container', '.container-fluid', '.stats-grid', '.row'];
    
    elements.forEach(selector => {
        const $el = $(selector);
        if ($el.length) {
            const el = $el[0];
            const hasOverflow = el.scrollWidth > el.clientWidth;
            const width = $el.outerWidth();
            const scrollWidth = el.scrollWidth;
            
            results.push({
                selector: selector,
                hasOverflow: hasOverflow,
                width: width,
                scrollWidth: scrollWidth,
                status: hasOverflow ? '❌ Overflow আছে' : '✅ ঠিক আছে'
            });
        }
    });
    
    showDebugResults('Overflow চেক', results);
}

function measureElements() {
    const results = [];
    const elements = ['.stats-grid', '.stat-card', '.quick-actions-grid', '.row', '.col-md-8', '.col-md-4'];
    
    elements.forEach(selector => {
        const $el = $(selector);
        if ($el.length) {
            const width = $el.outerWidth();
            const height = $el.outerHeight();
            const position = $el.offset();
            
            results.push({
                selector: selector,
                width: width + 'px',
                height: height + 'px',
                left: position ? position.left + 'px' : 'N/A',
                top: position ? position.top + 'px' : 'N/A'
            });
        }
    });
    
    showDebugResults('এলিমেন্ট মাপ', results);
}

function showViewportInfo() {
    const viewport = {
        width: $(window).width(),
        height: $(window).height(),
        devicePixelRatio: window.devicePixelRatio || 1,
        userAgent: navigator.userAgent,
        screenWidth: screen.width,
        screenHeight: screen.height
    };
    
    const breakpoint = getBreakpoint(viewport.width);
    
    const results = [
        { label: 'ভিউপোর্ট প্রস্থ', value: viewport.width + 'px' },
        { label: 'ভিউপোর্ট উচ্চতা', value: viewport.height + 'px' },
        { label: 'স্ক্রিন প্রস্থ', value: viewport.screenWidth + 'px' },
        { label: 'স্ক্রিন উচ্চতা', value: viewport.screenHeight + 'px' },
        { label: 'ডিভাইস পিক্সেল রেশিও', value: viewport.devicePixelRatio },
        { label: 'বর্তমান ব্রেকপয়েন্ট', value: breakpoint },
        { label: 'ইউজার এজেন্ট', value: viewport.userAgent.substring(0, 50) + '...' }
    ];
    
    showDebugResults('ভিউপোর্ট তথ্য', results);
}

function getBreakpoint(width) {
    if (width >= 1400) return 'XXL (≥1400px)';
    if (width >= 1200) return 'XL (≥1200px)';
    if (width >= 992) return 'LG (≥992px)';
    if (width >= 768) return 'MD (≥768px)';
    if (width >= 576) return 'SM (≥576px)';
    return 'XS (<576px)';
}

function showDebugResults(title, data) {
    let html = `<h4>${title}</h4><div class="table-responsive"><table class="table table-striped">`;
    
    if (Array.isArray(data)) {
        html += '<thead><tr>';
        Object.keys(data[0]).forEach(key => {
            html += `<th>${key}</th>`;
        });
        html += '</tr></thead><tbody>';
        
        data.forEach(row => {
            html += '<tr>';
            Object.values(row).forEach(value => {
                html += `<td>${value}</td>`;
            });
            html += '</tr>';
        });
        html += '</tbody>';
    }
    
    html += '</table></div>';
    
    $('#debug-output').html(html);
    $('#debug-results').show();
}

function resetDebug() {
    debugMode = false;
    $('*').css('border', '');
    $('#debug-results').hide();
    showNotification('ডিবাগ মোড রিসেট করা হয়েছে', 'success');
}

// Auto-check on page load
$(document).ready(function() {
    setTimeout(() => {
        showNotification('লেআউট ডিবাগ টুল লোড হয়েছে। উপরের বাটনগুলো ব্যবহার করুন।', 'info', 5000);
    }, 1000);
});
</script>

<?php include 'includes/footer.php'; ?>
