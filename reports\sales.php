<?php
require_once '../config/config.php';
requireAuth();

$page_title = 'বিক্রয় রিপোর্ট';

$db = new Database();
$conn = $db->getConnection();

// Date filters
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Get sales data
$query = "SELECT s.*, c.name as customer_name, u.full_name as created_by_name 
          FROM sales s 
          LEFT JOIN customers c ON s.customer_id = c.id 
          LEFT JOIN users u ON s.created_by = u.id 
          WHERE s.sale_date BETWEEN :start_date AND :end_date 
          ORDER BY s.sale_date DESC, s.created_at DESC";

$stmt = $conn->prepare($query);
$stmt->bindParam(':start_date', $start_date);
$stmt->bindParam(':end_date', $end_date);
$stmt->execute();
$sales = $stmt->fetchAll();

// Calculate totals
$total_sales = 0;
$total_paid = 0;
$total_due = 0;
$total_discount = 0;

foreach ($sales as $sale) {
    $total_sales += $sale['total_amount'];
    $total_paid += $sale['paid_amount'];
    $total_due += $sale['due_amount'];
    $total_discount += $sale['discount'];
}

include '../includes/header.php';
?>

<div class="main-content">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-chart-line"></i> বিক্রয় রিপোর্ট</h2>
            <button onclick="window.print();" class="btn btn-primary">
                <i class="fas fa-print"></i> প্রিন্ট
            </button>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row">
                    <div class="col-md-4">
                        <label for="start_date" class="form-label">শুরুর তারিখ</label>
                        <input type="date" id="start_date" name="start_date" class="form-control" 
                               value="<?php echo $start_date; ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="end_date" class="form-label">শেষের তারিখ</label>
                        <input type="date" id="end_date" name="end_date" class="form-control" 
                               value="<?php echo $end_date; ?>">
                    </div>
                    <div class="col-md-4">
                        <label>&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> রিপোর্ট দেখুন
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="stats-grid mb-4">
            <div class="stat-card success">
                <div class="stat-number"><?php echo formatCurrency($total_sales); ?></div>
                <div class="stat-label">মোট বিক্রয়</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo formatCurrency($total_paid); ?></div>
                <div class="stat-label">মোট আদায়</div>
            </div>
            
            <div class="stat-card warning">
                <div class="stat-number"><?php echo formatCurrency($total_due); ?></div>
                <div class="stat-label">মোট বকেয়া</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo toBengaliNumber(count($sales)); ?></div>
                <div class="stat-label">মোট লেনদেন</div>
            </div>
        </div>

        <!-- Sales Table -->
        <div class="card">
            <div class="card-header">
                <h5>বিক্রয় তালিকা (<?php echo formatDate($start_date); ?> থেকে <?php echo formatDate($end_date); ?>)</h5>
            </div>
            <div class="card-body">
                <?php if (empty($sales)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <p class="text-muted">নির্বাচিত সময়ে কোন বিক্রয় পাওয়া যায়নি।</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>তারিখ</th>
                                    <th>ইনভয়েস নং</th>
                                    <th>কাস্টমার</th>
                                    <th>মোট টাকা</th>
                                    <th>ছাড়</th>
                                    <th>প্রদত্ত</th>
                                    <th>বকেয়া</th>
                                    <th>বিক্রয়কারী</th>
                                    <th>স্ট্যাটাস</th>
                                    <th>কার্যক্রম</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($sales as $sale): ?>
                                    <tr>
                                        <td><?php echo formatDate($sale['sale_date']); ?></td>
                                        <td>
                                            <a href="../sales/invoice.php?id=<?php echo $sale['id']; ?>" target="_blank">
                                                <?php echo $sale['invoice_no']; ?>
                                            </a>
                                        </td>
                                        <td><?php echo $sale['customer_name'] ?? 'ওয়াক-ইন কাস্টমার'; ?></td>
                                        <td><?php echo formatCurrency($sale['total_amount']); ?></td>
                                        <td><?php echo formatCurrency($sale['discount']); ?></td>
                                        <td><?php echo formatCurrency($sale['paid_amount']); ?></td>
                                        <td>
                                            <?php if ($sale['due_amount'] > 0): ?>
                                                <span class="text-danger"><?php echo formatCurrency($sale['due_amount']); ?></span>
                                            <?php else: ?>
                                                <?php echo formatCurrency($sale['due_amount']); ?>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $sale['created_by_name']; ?></td>
                                        <td>
                                            <?php if ($sale['status'] == 'completed'): ?>
                                                <span class="badge badge-success">সম্পন্ন</span>
                                            <?php elseif ($sale['status'] == 'pending'): ?>
                                                <span class="badge badge-warning">অপেক্ষমাণ</span>
                                            <?php else: ?>
                                                <span class="badge badge-danger">বাতিল</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="../sales/invoice.php?id=<?php echo $sale['id']; ?>" 
                                               class="btn btn-sm btn-info" title="ইনভয়েস দেখুন" target="_blank">
                                                <i class="fas fa-file-invoice"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr class="table-info">
                                    <th colspan="3">মোট</th>
                                    <th><?php echo formatCurrency($total_sales); ?></th>
                                    <th><?php echo formatCurrency($total_discount); ?></th>
                                    <th><?php echo formatCurrency($total_paid); ?></th>
                                    <th><?php echo formatCurrency($total_due); ?></th>
                                    <th colspan="3"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .main-content .d-flex,
    .card:first-child {
        display: none !important;
    }
    
    .stats-grid {
        display: grid !important;
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 10px !important;
        margin-bottom: 20px !important;
    }
    
    .stat-card {
        border: 1px solid #ddd !important;
        padding: 10px !important;
        text-align: center !important;
    }
    
    .table {
        font-size: 12px !important;
    }
    
    .table th,
    .table td {
        padding: 5px !important;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
