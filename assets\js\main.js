// দোকান ম্যানেজমেন্ট সিস্টেম জাভাস্ক্রিপ্ট
// Shop Management System JavaScript

$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Modern navbar scroll effect
    let lastScrollTop = 0;
    $(window).scroll(function() {
        let scrollTop = $(this).scrollTop();

        if (scrollTop > 50) {
            $('.navbar').addClass('scrolled');
        } else {
            $('.navbar').removeClass('scrolled');
        }

        // Hide/show navbar on scroll
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            $('.navbar').addClass('navbar-hidden');
        } else {
            $('.navbar').removeClass('navbar-hidden');
        }
        lastScrollTop = scrollTop;
    });

    // Add stagger animation to menu items
    $('.navbar a').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
        $(this).addClass('menu-item-animate');
    });

    // User dropdown functionality
    $('#user-dropdown-btn').on('click', function(e) {
        e.stopPropagation();
        $('.user-dropdown').toggleClass('active');
    });

    // Close user dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.user-dropdown').length) {
            $('.user-dropdown').removeClass('active');
        }
    });

    // Global search functionality
    $('#global-search').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        if (searchTerm.length > 2) {
            performGlobalSearch(searchTerm);
        }
    });

    // Search button click
    $('.search-btn').on('click', function() {
        const searchTerm = $('#global-search').val();
        if (searchTerm.trim()) {
            performGlobalSearch(searchTerm);
        }
    });

    // Enter key search
    $('#global-search').on('keypress', function(e) {
        if (e.which === 13) {
            const searchTerm = $(this).val();
            if (searchTerm.trim()) {
                performGlobalSearch(searchTerm);
            }
        }
    });

    // Mobile menu toggle with animation
    $('#mobile-menu-toggle').on('click', function() {
        const menu = $('#navbar-menu');
        const icon = $(this).find('i');

        if (menu.hasClass('show')) {
            menu.removeClass('show');
            icon.removeClass('fa-times').addClass('fa-bars');
        } else {
            menu.addClass('show');
            icon.removeClass('fa-bars').addClass('fa-times');
        }
    });

    // Mobile dropdown toggle
    $('.dropdown > a').on('click', function(e) {
        if ($(window).width() <= 768) {
            e.preventDefault();
            e.stopPropagation();

            // Close other dropdowns
            $('.dropdown').not($(this).parent()).removeClass('active');

            // Toggle current dropdown
            $(this).parent().toggleClass('active');
        }
    });

    // Desktop dropdown hover management with improved stability
    let hoverTimeout;

    $('.dropdown').on('mouseenter', function() {
        if ($(window).width() > 768) {
            const $this = $(this);
            clearTimeout($this.data('timeout'));
            clearTimeout(hoverTimeout);

            // Add small delay to prevent flickering
            hoverTimeout = setTimeout(function() {
                $this.addClass('show-dropdown');
            }, 50);
        }
    }).on('mouseleave', function() {
        if ($(window).width() > 768) {
            const $this = $(this);
            clearTimeout(hoverTimeout);

            const timeout = setTimeout(function() {
                $this.removeClass('show-dropdown');
            }, 200);
            $this.data('timeout', timeout);
        }
    });

    // Also handle dropdown menu hover to prevent closing
    $('.dropdown-menu').on('mouseenter', function() {
        if ($(window).width() > 768) {
            const $dropdown = $(this).closest('.dropdown');
            clearTimeout($dropdown.data('timeout'));
        }
    }).on('mouseleave', function() {
        if ($(window).width() > 768) {
            const $dropdown = $(this).closest('.dropdown');
            const timeout = setTimeout(function() {
                $dropdown.removeClass('show-dropdown');
            }, 200);
            $dropdown.data('timeout', timeout);
        }
    });

    // Close mobile menu when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.navbar').length) {
            $('#navbar-menu').removeClass('show');
            $('#mobile-menu-toggle i').removeClass('fa-times').addClass('fa-bars');
            $('.dropdown').removeClass('active');
        }
    });

    // Close mobile menu when window is resized to desktop
    $(window).on('resize', function() {
        if ($(window).width() > 768) {
            $('#navbar-menu').removeClass('show');
            $('#mobile-menu-toggle i').removeClass('fa-times').addClass('fa-bars');
            $('.dropdown').removeClass('active');
        }
    });
    
    // Confirm delete actions
    $('.btn-delete').on('click', function(e) {
        if (!confirm('আপনি কি নিশ্চিত যে এটি মুছে ফেলতে চান?')) {
            e.preventDefault();
        }
    });
    
    // Format currency inputs
    $('.currency-input').on('input', function() {
        let value = $(this).val().replace(/[^\d.]/g, '');
        $(this).val(value);
    });
    
    // Auto-calculate totals in forms
    $('.calculate-total').on('input', function() {
        calculateTotal();
    });
    
    // Search functionality
    $('#search-input').on('keyup', function() {
        let value = $(this).val().toLowerCase();
        $('#data-table tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
    
    // Print functionality
    $('.btn-print').on('click', function() {
        window.print();
    });
    
    // Export functionality
    $('.btn-export').on('click', function() {
        let table = $('#data-table');
        let csv = tableToCSV(table);
        downloadCSV(csv, 'export.csv');
    });
});

// Calculate total function
function calculateTotal() {
    let total = 0;
    $('.item-total').each(function() {
        let value = parseFloat($(this).val()) || 0;
        total += value;
    });
    $('#total-amount').val(total.toFixed(2));
    
    // Calculate due amount
    let paid = parseFloat($('#paid-amount').val()) || 0;
    let due = total - paid;
    $('#due-amount').val(due.toFixed(2));
}

// Add new row to dynamic table
function addRow(tableId) {
    let table = $('#' + tableId + ' tbody');
    let rowCount = table.find('tr').length;
    let newRow = table.find('tr:first').clone();
    
    // Clear input values
    newRow.find('input, select').val('');
    newRow.find('input[type="text"], input[type="number"]').val('');
    
    // Update name attributes
    newRow.find('input, select').each(function() {
        let name = $(this).attr('name');
        if (name) {
            $(this).attr('name', name.replace(/\[\d+\]/, '[' + rowCount + ']'));
        }
    });
    
    table.append(newRow);
}

// Remove row from dynamic table
function removeRow(button) {
    let table = $(button).closest('table');
    let rowCount = table.find('tbody tr').length;
    
    if (rowCount > 1) {
        $(button).closest('tr').remove();
        calculateTotal();
    } else {
        alert('অন্তত একটি আইটেম থাকতে হবে।');
    }
}

// Product search with autocomplete
function initProductSearch() {
    $('.product-search').autocomplete({
        source: function(request, response) {
            $.ajax({
                url: '../ajax/search_products.php',
                type: 'GET',
                data: {
                    term: request.term
                },
                success: function(data) {
                    response(data);
                },
                error: function() {
                    response([]);
                }
            });
        },
        minLength: 2,
        select: function(event, ui) {
            let row = $(this).closest('tr');
            row.find('.product-id').val(ui.item.id);
            row.find('.unit-price').val(ui.item.selling_price);
            calculateRowTotal(row[0].querySelector('.quantity'));
            return false;
        }
    });
}

// Calculate row total
function calculateRowTotal(row) {
    let quantity = parseFloat(row.find('.quantity').val()) || 0;
    let unitPrice = parseFloat(row.find('.unit-price').val()) || 0;
    let total = quantity * unitPrice;
    
    row.find('.row-total').val(total.toFixed(2));
    calculateTotal();
}

// Customer search with autocomplete
function initCustomerSearch() {
    $('.customer-search').autocomplete({
        source: function(request, response) {
            $.ajax({
                url: '../ajax/search_customers.php',
                type: 'GET',
                data: {
                    term: request.term
                },
                success: function(data) {
                    response(data);
                },
                error: function() {
                    response([]);
                }
            });
        },
        minLength: 2,
        select: function(event, ui) {
            $('#customer_id').val(ui.item.id);
            return false;
        }
    });
}

// Supplier search with autocomplete
function initSupplierSearch() {
    $('.supplier-search').autocomplete({
        source: function(request, response) {
            $.ajax({
                url: 'ajax/search_suppliers.php',
                type: 'GET',
                data: {
                    term: request.term
                },
                success: function(data) {
                    response(JSON.parse(data));
                }
            });
        },
        minLength: 2,
        select: function(event, ui) {
            $('#supplier-id').val(ui.item.id);
            $('#supplier-name').val(ui.item.name);
            $('#supplier-phone').val(ui.item.phone);
            $('#supplier-address').val(ui.item.address);
        }
    });
}

// Show loading spinner
function showLoading() {
    $('#loading-screen').css('display', 'flex');
}

// Hide loading spinner
function hideLoading() {
    $('#loading-screen').hide();
}

// Show page loading
function showPageLoading() {
    $('.navbar').addClass('loading');
    showLoading();
}

// Hide page loading
function hidePageLoading() {
    $('.navbar').removeClass('loading');
    hideLoading();
}

// Show success message
function showSuccess(message) {
    showAlert(message, 'success');
}

// Show error message
function showError(message) {
    showAlert(message, 'danger');
}

// Show alert message
function showAlert(message, type) {
    let alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                    message +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '</div>';
    
    $('#alerts-container').html(alertHtml);
    
    // Auto-hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
}

// Convert table to CSV
function tableToCSV(table) {
    let csv = [];
    let rows = table.find('tr');
    
    for (let i = 0; i < rows.length; i++) {
        let row = [], cols = $(rows[i]).find('td, th');
        
        for (let j = 0; j < cols.length; j++) {
            let cellText = $(cols[j]).text().trim();
            row.push('"' + cellText + '"');
        }
        
        csv.push(row.join(','));
    }
    
    return csv.join('\n');
}

// Download CSV file
function downloadCSV(csv, filename) {
    let csvFile = new Blob([csv], {type: 'text/csv'});
    let downloadLink = document.createElement('a');
    
    downloadLink.download = filename;
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';
    
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

// Format number with Bengali digits
function toBengaliNumber(number) {
    const bengaliDigits = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
    const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    let bengaliNumber = number.toString();
    for (let i = 0; i < englishDigits.length; i++) {
        bengaliNumber = bengaliNumber.replace(new RegExp(englishDigits[i], 'g'), bengaliDigits[i]);
    }
    
    return bengaliNumber;
}

// Format currency
function formatCurrency(amount) {
    return '৳ ' + parseFloat(amount).toFixed(2);
}

// Validate form
function validateForm(formId) {
    let isValid = true;
    let form = $('#' + formId);
    
    form.find('.required').each(function() {
        if ($(this).val().trim() === '') {
            $(this).addClass('is-invalid');
            isValid = false;
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    return isValid;
}

// Reset form
function resetForm(formId) {
    $('#' + formId)[0].reset();
    $('#' + formId + ' .is-invalid').removeClass('is-invalid');
}

// Confirm action
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// Menu utilities
function setActiveMenu(menuItem) {
    $('.navbar a').removeClass('active');
    $(menuItem).addClass('active');
}

function highlightCurrentPage() {
    const currentPath = window.location.pathname;
    $('.navbar a').each(function() {
        const href = $(this).attr('href');
        if (href && currentPath.includes(href.split('/').pop())) {
            $(this).addClass('active');
            $(this).closest('.dropdown').find('> a').addClass('active');
        }
    });
}

// Initialize menu highlighting
$(document).ready(function() {
    highlightCurrentPage();
});

// Smooth scroll for anchor links
$('a[href^="#"]').on('click', function(e) {
    e.preventDefault();
    const target = $(this.getAttribute('href'));
    if (target.length) {
        $('html, body').stop().animate({
            scrollTop: target.offset().top - 100
        }, 1000);
    }
});

// Add loading state to forms
$('form').on('submit', function() {
    const submitBtn = $(this).find('button[type="submit"], input[type="submit"]');
    submitBtn.prop('disabled', true);
    submitBtn.html('<i class="fas fa-spinner fa-spin"></i> প্রক্রিয়াকরণ...');
    showLoading();
});

// Auto-hide alerts with animation
function autoHideAlerts() {
    $('.alert').each(function() {
        const alert = $(this);
        setTimeout(function() {
            alert.fadeOut('slow', function() {
                alert.remove();
            });
        }, 5000);
    });
}

// Initialize auto-hide alerts
$(document).ready(function() {
    autoHideAlerts();
});

// Scroll to Top functionality
$(window).scroll(function() {
    if ($(this).scrollTop() > 300) {
        $('#scrollToTop').addClass('show');
    } else {
        $('#scrollToTop').removeClass('show');
    }
});

$('#scrollToTop').on('click', function() {
    $('html, body').animate({
        scrollTop: 0
    }, 800);
});

// Menu notification badge
function addMenuBadge(menuSelector, count) {
    const menu = $(menuSelector);
    menu.find('.menu-badge').remove();
    if (count > 0) {
        menu.append('<span class="menu-badge">' + count + '</span>');
    }
}

// Keyboard navigation for menu
$(document).on('keydown', function(e) {
    if (e.key === 'Escape') {
        $('#navbar-menu').removeClass('show');
        $('#mobile-menu-toggle i').removeClass('fa-times').addClass('fa-bars');
        $('.dropdown').removeClass('active');
    }
});

// Menu search functionality
function initMenuSearch() {
    const searchInput = $('<input type="text" placeholder="মেনু খুঁজুন..." class="menu-search" style="display: none; position: absolute; top: 100%; left: 0; width: 200px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; z-index: 1002;">');
    $('.navbar').append(searchInput);

    $(document).on('keydown', function(e) {
        if (e.ctrlKey && e.key === 'k') {
            e.preventDefault();
            searchInput.toggle().focus();
        }
    });

    searchInput.on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        $('.navbar a').each(function() {
            const text = $(this).text().toLowerCase();
            if (text.includes(searchTerm)) {
                $(this).parent().show();
            } else {
                $(this).parent().hide();
            }
        });
    });
}

// Initialize menu search
$(document).ready(function() {
    initMenuSearch();
});

// Enhanced notification system
function showNotification(message, type = 'info', duration = 5000) {
    const notification = $(`
        <div class="notification notification-${type}">
            <div class="notification-content">
                <i class="notification-icon fas ${getNotificationIcon(type)}"></i>
                <span class="notification-message">${message}</span>
                <button class="notification-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `);

    $('body').append(notification);

    // Animate in
    setTimeout(() => {
        notification.addClass('show');
    }, 100);

    // Auto remove
    setTimeout(() => {
        removeNotification(notification);
    }, duration);

    // Manual close
    notification.find('.notification-close').on('click', function() {
        removeNotification(notification);
    });
}

function removeNotification(notification) {
    notification.removeClass('show');
    setTimeout(() => {
        notification.remove();
    }, 300);
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };
    return icons[type] || icons['info'];
}

// Menu performance optimization
function optimizeMenu() {
    // Lazy load dropdown menus
    $('.dropdown').on('mouseenter', function() {
        const dropdown = $(this);
        if (!dropdown.data('loaded')) {
            dropdown.data('loaded', true);
            // Add any dynamic content loading here
        }
    });

    // Debounce menu search
    let searchTimeout;
    $('.menu-search').on('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = $(this).val();
        searchTimeout = setTimeout(() => {
            performMenuSearch(searchTerm);
        }, 300);
    });
}

function performMenuSearch(term) {
    const searchTerm = term.toLowerCase();
    $('.navbar a').each(function() {
        const text = $(this).text().toLowerCase();
        const parent = $(this).parent();
        if (text.includes(searchTerm) || searchTerm === '') {
            parent.show();
        } else {
            parent.hide();
        }
    });
}

// Initialize optimizations
$(document).ready(function() {
    optimizeMenu();
});

// Global search function
function performGlobalSearch(searchTerm) {
    showLoading();

    // Simulate search (replace with actual AJAX call)
    setTimeout(function() {
        hideLoading();

        // Show search results notification
        showNotification(`"${searchTerm}" এর জন্য অনুসন্ধান সম্পন্ন হয়েছে`, 'info');

        // Here you would typically redirect to search results page
        // window.location.href = `search.php?q=${encodeURIComponent(searchTerm)}`;
    }, 1000);
}

// Enhanced menu interactions
function initAdvancedMenuFeatures() {
    // Add ripple effect to menu items
    $('.navbar a').on('click', function(e) {
        const ripple = $('<span class="ripple"></span>');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.css({
            width: size,
            height: size,
            left: x,
            top: y
        });

        $(this).append(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    });

    // Add hover sound effect (optional)
    $('.navbar a').on('mouseenter', function() {
        // You can add sound effects here if needed
        $(this).addClass('menu-hover-effect');
    }).on('mouseleave', function() {
        $(this).removeClass('menu-hover-effect');
    });

    // Keyboard navigation enhancement
    $(document).on('keydown', function(e) {
        if (e.altKey && e.key >= '1' && e.key <= '9') {
            e.preventDefault();
            const index = parseInt(e.key) - 1;
            const menuItem = $('.navbar a').eq(index);
            if (menuItem.length) {
                menuItem[0].click();
            }
        }
    });
}

// Initialize advanced features
$(document).ready(function() {
    initAdvancedMenuFeatures();
});

// Menu performance monitoring
function trackMenuPerformance() {
    const menuItems = $('.navbar a');
    let hoverCount = 0;
    let clickCount = 0;

    menuItems.on('mouseenter', function() {
        hoverCount++;
    });

    menuItems.on('click', function() {
        clickCount++;
        console.log(`Menu performance: ${clickCount} clicks, ${hoverCount} hovers`);
    });
}

// Initialize performance tracking
$(document).ready(function() {
    trackMenuPerformance();
});
