// দোকান ম্যানেজমেন্ট সিস্টেম জাভাস্ক্রিপ্ট
// Shop Management System JavaScript

$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
    
    // Confirm delete actions
    $('.btn-delete').on('click', function(e) {
        if (!confirm('আপনি কি নিশ্চিত যে এটি মুছে ফেলতে চান?')) {
            e.preventDefault();
        }
    });
    
    // Format currency inputs
    $('.currency-input').on('input', function() {
        let value = $(this).val().replace(/[^\d.]/g, '');
        $(this).val(value);
    });
    
    // Auto-calculate totals in forms
    $('.calculate-total').on('input', function() {
        calculateTotal();
    });
    
    // Search functionality
    $('#search-input').on('keyup', function() {
        let value = $(this).val().toLowerCase();
        $('#data-table tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
    
    // Print functionality
    $('.btn-print').on('click', function() {
        window.print();
    });
    
    // Export functionality
    $('.btn-export').on('click', function() {
        let table = $('#data-table');
        let csv = tableToCSV(table);
        downloadCSV(csv, 'export.csv');
    });
});

// Calculate total function
function calculateTotal() {
    let total = 0;
    $('.item-total').each(function() {
        let value = parseFloat($(this).val()) || 0;
        total += value;
    });
    $('#total-amount').val(total.toFixed(2));
    
    // Calculate due amount
    let paid = parseFloat($('#paid-amount').val()) || 0;
    let due = total - paid;
    $('#due-amount').val(due.toFixed(2));
}

// Add new row to dynamic table
function addRow(tableId) {
    let table = $('#' + tableId + ' tbody');
    let rowCount = table.find('tr').length;
    let newRow = table.find('tr:first').clone();
    
    // Clear input values
    newRow.find('input, select').val('');
    newRow.find('input[type="text"], input[type="number"]').val('');
    
    // Update name attributes
    newRow.find('input, select').each(function() {
        let name = $(this).attr('name');
        if (name) {
            $(this).attr('name', name.replace(/\[\d+\]/, '[' + rowCount + ']'));
        }
    });
    
    table.append(newRow);
}

// Remove row from dynamic table
function removeRow(button) {
    let table = $(button).closest('table');
    let rowCount = table.find('tbody tr').length;
    
    if (rowCount > 1) {
        $(button).closest('tr').remove();
        calculateTotal();
    } else {
        alert('অন্তত একটি আইটেম থাকতে হবে।');
    }
}

// Product search with autocomplete
function initProductSearch() {
    $('.product-search').autocomplete({
        source: function(request, response) {
            $.ajax({
                url: '../ajax/search_products.php',
                type: 'GET',
                data: {
                    term: request.term
                },
                success: function(data) {
                    response(data);
                },
                error: function() {
                    response([]);
                }
            });
        },
        minLength: 2,
        select: function(event, ui) {
            let row = $(this).closest('tr');
            row.find('.product-id').val(ui.item.id);
            row.find('.unit-price').val(ui.item.selling_price);
            calculateRowTotal(row[0].querySelector('.quantity'));
            return false;
        }
    });
}

// Calculate row total
function calculateRowTotal(row) {
    let quantity = parseFloat(row.find('.quantity').val()) || 0;
    let unitPrice = parseFloat(row.find('.unit-price').val()) || 0;
    let total = quantity * unitPrice;
    
    row.find('.row-total').val(total.toFixed(2));
    calculateTotal();
}

// Customer search with autocomplete
function initCustomerSearch() {
    $('.customer-search').autocomplete({
        source: function(request, response) {
            $.ajax({
                url: '../ajax/search_customers.php',
                type: 'GET',
                data: {
                    term: request.term
                },
                success: function(data) {
                    response(data);
                },
                error: function() {
                    response([]);
                }
            });
        },
        minLength: 2,
        select: function(event, ui) {
            $('#customer_id').val(ui.item.id);
            return false;
        }
    });
}

// Supplier search with autocomplete
function initSupplierSearch() {
    $('.supplier-search').autocomplete({
        source: function(request, response) {
            $.ajax({
                url: 'ajax/search_suppliers.php',
                type: 'GET',
                data: {
                    term: request.term
                },
                success: function(data) {
                    response(JSON.parse(data));
                }
            });
        },
        minLength: 2,
        select: function(event, ui) {
            $('#supplier-id').val(ui.item.id);
            $('#supplier-name').val(ui.item.name);
            $('#supplier-phone').val(ui.item.phone);
            $('#supplier-address').val(ui.item.address);
        }
    });
}

// Show loading spinner
function showLoading() {
    $('#loading').show();
}

// Hide loading spinner
function hideLoading() {
    $('#loading').hide();
}

// Show success message
function showSuccess(message) {
    showAlert(message, 'success');
}

// Show error message
function showError(message) {
    showAlert(message, 'danger');
}

// Show alert message
function showAlert(message, type) {
    let alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                    message +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '</div>';
    
    $('#alerts-container').html(alertHtml);
    
    // Auto-hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
}

// Convert table to CSV
function tableToCSV(table) {
    let csv = [];
    let rows = table.find('tr');
    
    for (let i = 0; i < rows.length; i++) {
        let row = [], cols = $(rows[i]).find('td, th');
        
        for (let j = 0; j < cols.length; j++) {
            let cellText = $(cols[j]).text().trim();
            row.push('"' + cellText + '"');
        }
        
        csv.push(row.join(','));
    }
    
    return csv.join('\n');
}

// Download CSV file
function downloadCSV(csv, filename) {
    let csvFile = new Blob([csv], {type: 'text/csv'});
    let downloadLink = document.createElement('a');
    
    downloadLink.download = filename;
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';
    
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

// Format number with Bengali digits
function toBengaliNumber(number) {
    const bengaliDigits = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
    const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    let bengaliNumber = number.toString();
    for (let i = 0; i < englishDigits.length; i++) {
        bengaliNumber = bengaliNumber.replace(new RegExp(englishDigits[i], 'g'), bengaliDigits[i]);
    }
    
    return bengaliNumber;
}

// Format currency
function formatCurrency(amount) {
    return '৳ ' + parseFloat(amount).toFixed(2);
}

// Validate form
function validateForm(formId) {
    let isValid = true;
    let form = $('#' + formId);
    
    form.find('.required').each(function() {
        if ($(this).val().trim() === '') {
            $(this).addClass('is-invalid');
            isValid = false;
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    return isValid;
}

// Reset form
function resetForm(formId) {
    $('#' + formId)[0].reset();
    $('#' + formId + ' .is-invalid').removeClass('is-invalid');
}

// Confirm action
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}
