<?php
require_once 'config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect(BASE_URL . 'dashboard.php');
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error = 'ইউজারনেম এবং পাসওয়ার্ড প্রয়োজন।';
    } else {
        if (login($username, $password)) {
            redirect(BASE_URL . 'dashboard.php');
        } else {
            $error = 'ভুল ইউজারনেম অথবা পাসওয়ার্ড।';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>লগইন - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>css/style.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .login-header p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-control {
            padding: 15px;
            font-size: 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn-login {
            width: 100%;
            padding: 15px;
            font-size: 16px;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
        }
        
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
            border: 1px solid #f5c6cb;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><?php echo APP_NAME; ?></h1>
            <p>আপনার অ্যাকাউন্টে লগইন করুন</p>
        </div>
        
        <?php if (!empty($error)): ?>
            <div class="error-message">
                <?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="form-group">
                <label for="username" class="form-label">ইউজারনেম</label>
                <input type="text" id="username" name="username" class="form-control" 
                       placeholder="আপনার ইউজারনেম লিখুন" required 
                       value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">পাসওয়ার্ড</label>
                <input type="password" id="password" name="password" class="form-control" 
                       placeholder="আপনার পাসওয়ার্ড লিখুন" required>
            </div>
            
            <button type="submit" class="btn-login">লগইন করুন</button>
        </form>
        
        <div class="login-footer">
            <p>ডিফল্ট লগইন: <strong>admin</strong> / <strong>password</strong></p>
            <p>&copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. সকল অধিকার সংরক্ষিত।</p>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Focus on username field
            $('#username').focus();
            
            // Handle form submission
            $('form').on('submit', function() {
                let username = $('#username').val().trim();
                let password = $('#password').val().trim();
                
                if (username === '' || password === '') {
                    alert('ইউজারনেম এবং পাসওয়ার্ড প্রয়োজন।');
                    return false;
                }
            });
        });
    </script>
</body>
</html>
