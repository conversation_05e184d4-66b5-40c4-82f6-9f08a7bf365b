<?php
require_once '../config/config.php';
requireAuth();

$page_title = 'নতুন পণ্য যোগ করুন';

$db = new Database();
$conn = $db->getConnection();

// Get categories
$query = "SELECT * FROM categories WHERE status = 'active' ORDER BY name";
$stmt = $conn->prepare($query);
$stmt->execute();
$categories = $stmt->fetchAll();

$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate input
    $name = sanitize($_POST['name']);
    $category_id = (int)$_POST['category_id'];
    $barcode = sanitize($_POST['barcode']);
    $unit = sanitize($_POST['unit']);
    $purchase_price = (float)$_POST['purchase_price'];
    $selling_price = (float)$_POST['selling_price'];
    $stock_quantity = (int)$_POST['stock_quantity'];
    $min_stock_level = (int)$_POST['min_stock_level'];
    $expiry_date = !empty($_POST['expiry_date']) ? $_POST['expiry_date'] : null;
    $description = sanitize($_POST['description']);
    $status = sanitize($_POST['status']);

    // Validation
    if (empty($name)) {
        $errors[] = 'পণ্যের নাম প্রয়োজন।';
    }

    if ($purchase_price <= 0) {
        $errors[] = 'ক্রয় মূল্য ০ এর চেয়ে বেশি হতে হবে।';
    }

    if ($selling_price <= 0) {
        $errors[] = 'বিক্রয় মূল্য ০ এর চেয়ে বেশি হতে হবে।';
    }

    if ($stock_quantity < 0) {
        $errors[] = 'স্টক পরিমাণ ০ বা তার চেয়ে বেশি হতে হবে।';
    }

    if ($min_stock_level < 0) {
        $errors[] = 'সর্বনিম্ন স্টক লেভেল ০ বা তার চেয়ে বেশি হতে হবে।';
    }

    // Check if barcode already exists
    if (!empty($barcode)) {
        $query = "SELECT id FROM products WHERE barcode = :barcode";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':barcode', $barcode);
        $stmt->execute();
        if ($stmt->fetch()) {
            $errors[] = 'এই বারকোড ইতিমধ্যে ব্যবহৃত হয়েছে।';
        }
    }

    if (empty($errors)) {
        try {
            $conn->beginTransaction();

            // Insert product
            $query = "INSERT INTO products (name, category_id, barcode, unit, purchase_price, selling_price, 
                      stock_quantity, min_stock_level, expiry_date, description, status) 
                      VALUES (:name, :category_id, :barcode, :unit, :purchase_price, :selling_price, 
                      :stock_quantity, :min_stock_level, :expiry_date, :description, :status)";
            
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':category_id', $category_id);
            $stmt->bindParam(':barcode', $barcode);
            $stmt->bindParam(':unit', $unit);
            $stmt->bindParam(':purchase_price', $purchase_price);
            $stmt->bindParam(':selling_price', $selling_price);
            $stmt->bindParam(':stock_quantity', $stock_quantity);
            $stmt->bindParam(':min_stock_level', $min_stock_level);
            $stmt->bindParam(':expiry_date', $expiry_date);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':status', $status);
            
            $stmt->execute();
            $product_id = $conn->lastInsertId();

            // Record initial stock movement if stock > 0
            if ($stock_quantity > 0) {
                updateProductStock($product_id, $stock_quantity, 'in', 'adjustment');
            }

            $conn->commit();
            
            logActivity('Product Added', "Added product: $name");
            showAlert('পণ্য সফলভাবে যোগ করা হয়েছে।', 'success');
            $success = true;
            
            // Reset form
            $_POST = [];
            
        } catch (Exception $e) {
            $conn->rollback();
            $errors[] = 'পণ্য যোগ করতে সমস্যা হয়েছে: ' . $e->getMessage();
        }
    }
}

include '../includes/header.php';
?>

<div class="main-content">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-plus"></i> নতুন পণ্য যোগ করুন</h2>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> পণ্য তালিকায় ফিরুন
            </a>
        </div>

        <!-- Error Messages -->
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- Add Product Form -->
        <div class="card">
            <div class="card-header">
                <h5>পণ্যের তথ্য</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="" id="product-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name" class="form-label">পণ্যের নাম *</label>
                                <input type="text" id="name" name="name" class="form-control required" 
                                       placeholder="পণ্যের নাম লিখুন" 
                                       value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="category_id" class="form-label">ক্যাটাগরি</label>
                                <select id="category_id" name="category_id" class="form-control">
                                    <option value="">ক্যাটাগরি নির্বাচন করুন</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" 
                                                <?php echo (isset($_POST['category_id']) && $_POST['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                            <?php echo $category['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="barcode" class="form-label">বারকোড</label>
                                <input type="text" id="barcode" name="barcode" class="form-control" 
                                       placeholder="বারকোড (ঐচ্ছিক)" 
                                       value="<?php echo isset($_POST['barcode']) ? htmlspecialchars($_POST['barcode']) : ''; ?>">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="unit" class="form-label">একক</label>
                                <select id="unit" name="unit" class="form-control">
                                    <option value="pcs" <?php echo (isset($_POST['unit']) && $_POST['unit'] == 'pcs') ? 'selected' : ''; ?>>পিস</option>
                                    <option value="kg" <?php echo (isset($_POST['unit']) && $_POST['unit'] == 'kg') ? 'selected' : ''; ?>>কেজি</option>
                                    <option value="ltr" <?php echo (isset($_POST['unit']) && $_POST['unit'] == 'ltr') ? 'selected' : ''; ?>>লিটার</option>
                                    <option value="box" <?php echo (isset($_POST['unit']) && $_POST['unit'] == 'box') ? 'selected' : ''; ?>>বক্স</option>
                                    <option value="packet" <?php echo (isset($_POST['unit']) && $_POST['unit'] == 'packet') ? 'selected' : ''; ?>>প্যাকেট</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="purchase_price" class="form-label">ক্রয় মূল্য *</label>
                                <input type="number" id="purchase_price" name="purchase_price" class="form-control required currency-input" 
                                       step="0.01" min="0" placeholder="0.00" 
                                       value="<?php echo isset($_POST['purchase_price']) ? $_POST['purchase_price'] : ''; ?>">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="selling_price" class="form-label">বিক্রয় মূল্য *</label>
                                <input type="number" id="selling_price" name="selling_price" class="form-control required currency-input" 
                                       step="0.01" min="0" placeholder="0.00" 
                                       value="<?php echo isset($_POST['selling_price']) ? $_POST['selling_price'] : ''; ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="stock_quantity" class="form-label">প্রাথমিক স্টক</label>
                                <input type="number" id="stock_quantity" name="stock_quantity" class="form-control" 
                                       min="0" placeholder="0" 
                                       value="<?php echo isset($_POST['stock_quantity']) ? $_POST['stock_quantity'] : '0'; ?>">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="min_stock_level" class="form-label">সর্বনিম্ন স্টক লেভেল</label>
                                <input type="number" id="min_stock_level" name="min_stock_level" class="form-control" 
                                       min="0" placeholder="0" 
                                       value="<?php echo isset($_POST['min_stock_level']) ? $_POST['min_stock_level'] : '5'; ?>">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="expiry_date" class="form-label">মেয়াদ উত্তীর্ণের তারিখ</label>
                                <input type="date" id="expiry_date" name="expiry_date" class="form-control" 
                                       value="<?php echo isset($_POST['expiry_date']) ? $_POST['expiry_date'] : ''; ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="description" class="form-label">বিবরণ</label>
                                <textarea id="description" name="description" class="form-control" rows="3" 
                                          placeholder="পণ্যের বিবরণ (ঐচ্ছিক)"><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="status" class="form-label">স্ট্যাটাস</label>
                                <select id="status" name="status" class="form-control">
                                    <option value="active" <?php echo (isset($_POST['status']) && $_POST['status'] == 'active') ? 'selected' : ''; ?>>সক্রিয়</option>
                                    <option value="inactive" <?php echo (isset($_POST['status']) && $_POST['status'] == 'inactive') ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> পণ্য সংরক্ষণ করুন
                        </button>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-times"></i> বাতিল
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Form validation
    $('#product-form').on('submit', function(e) {
        if (!validateForm('product-form')) {
            e.preventDefault();
            showError('দয়া করে সকল প্রয়োজনীয় ক্ষেত্র পূরণ করুন।');
        }
    });

    // Auto-calculate profit margin
    $('#purchase_price, #selling_price').on('input', function() {
        let purchasePrice = parseFloat($('#purchase_price').val()) || 0;
        let sellingPrice = parseFloat($('#selling_price').val()) || 0;
        
        if (purchasePrice > 0 && sellingPrice > 0) {
            let profit = sellingPrice - purchasePrice;
            let margin = (profit / purchasePrice) * 100;
            
            if (profit < 0) {
                showError('বিক্রয় মূল্য ক্রয় মূল্যের চেয়ে কম!');
            }
        }
    });

    // Generate barcode
    $('#generate-barcode').on('click', function() {
        let barcode = Date.now().toString();
        $('#barcode').val(barcode);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
