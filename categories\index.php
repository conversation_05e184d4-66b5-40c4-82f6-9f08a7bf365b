<?php
// Categories Management - Ultra Modern Design
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/safe_functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

$page_title = 'ক্যাটাগরি ব্যবস্থাপনা';

// Breadcrumbs
$breadcrumbs = [
    ['title' => 'ড্যাশবোর্ড', 'url' => '../dashboard.php', 'icon' => 'fas fa-home'],
    ['title' => 'ক্যাটাগরি ব্যবস্থাপনা', 'icon' => 'fas fa-tags']
];

// Sample categories data (replace with actual database query)
$categories = [
    [
        'id' => 1,
        'name' => 'খাদ্য পণ্য',
        'description' => 'সকল প্রকার খাদ্য সামগ্রী',
        'products_count' => 45,
        'status' => 'active',
        'created_at' => '2024-01-15',
        'image' => 'food.jpg'
    ],
    [
        'id' => 2,
        'name' => 'পানীয়',
        'description' => 'বিভিন্ন ধরনের পানীয়',
        'products_count' => 23,
        'status' => 'active',
        'created_at' => '2024-01-20',
        'image' => 'drinks.jpg'
    ],
    [
        'id' => 3,
        'name' => 'ইলেকট্রনিক্স',
        'description' => 'ইলেকট্রনিক পণ্যসমূহ',
        'products_count' => 67,
        'status' => 'active',
        'created_at' => '2024-02-01',
        'image' => 'electronics.jpg'
    ],
    [
        'id' => 4,
        'name' => 'পোশাক',
        'description' => 'পুরুষ ও মহিলাদের পোশাক',
        'products_count' => 89,
        'status' => 'active',
        'created_at' => '2024-02-10',
        'image' => 'clothing.jpg'
    ],
    [
        'id' => 5,
        'name' => 'বই ও স্টেশনারি',
        'description' => 'শিক্ষামূলক বই এবং অফিস সামগ্রী',
        'products_count' => 34,
        'status' => 'inactive',
        'created_at' => '2024-02-15',
        'image' => 'books.jpg'
    ],
    [
        'id' => 6,
        'name' => 'স্বাস্থ্য ও সৌন্দর্য',
        'description' => 'স্বাস্থ্য এবং সৌন্দর্য পণ্য',
        'products_count' => 56,
        'status' => 'active',
        'created_at' => '2024-02-20',
        'image' => 'health.jpg'
    ]
];

// Statistics
$total_categories = count($categories);
$active_categories = count(array_filter($categories, function($cat) { return $cat['status'] == 'active'; }));
$total_products = array_sum(array_column($categories, 'products_count'));

include '../includes/header.php';
?>

<div class="categories-container">
    <div class="container-fluid">
        <!-- Modern Page Header -->
        <div class="page-header-modern">
            <div class="header-content">
                <div class="header-title-section">
                    <h1 class="page-title">
                        <div class="title-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="title-text">
                            <span class="main-title">ক্যাটাগরি ব্যবস্থাপনা</span>
                            <span class="sub-title">পণ্যের ক্যাটাগরি সংগঠিত করুন</span>
                        </div>
                    </h1>
                </div>
                <div class="header-actions">
                    <button class="action-btn primary" onclick="openAddModal()">
                        <i class="fas fa-plus"></i>
                        নতুন ক্যাটাগরি
                    </button>
                    <button class="action-btn secondary" onclick="exportCategories()">
                        <i class="fas fa-download"></i>
                        এক্সপোর্ট
                    </button>
                    <button class="action-btn info" onclick="importCategories()">
                        <i class="fas fa-upload"></i>
                        ইমপোর্ট
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-row">
            <div class="stat-card-modern primary">
                <div class="stat-icon">
                    <i class="fas fa-layer-group"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo toBengaliNumber($total_categories); ?></div>
                    <div class="stat-label">মোট ক্যাটাগরি</div>
                </div>
                <div class="stat-trend">
                    <i class="fas fa-arrow-up"></i>
                    <span>+৩ এই মাসে</span>
                </div>
            </div>

            <div class="stat-card-modern success">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo toBengaliNumber($active_categories); ?></div>
                    <div class="stat-label">সক্রিয় ক্যাটাগরি</div>
                </div>
                <div class="stat-trend">
                    <i class="fas fa-arrow-up"></i>
                    <span>৯৫% সক্রিয়</span>
                </div>
            </div>

            <div class="stat-card-modern info">
                <div class="stat-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo toBengaliNumber($total_products); ?></div>
                    <div class="stat-label">মোট পণ্য</div>
                </div>
                <div class="stat-trend">
                    <i class="fas fa-arrow-up"></i>
                    <span>+১২ আজকে</span>
                </div>
            </div>

            <div class="stat-card-modern warning">
                <div class="stat-icon">
                    <i class="fas fa-chart-pie"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo toBengaliNumber(round($total_products / $total_categories)); ?></div>
                    <div class="stat-label">গড় পণ্য/ক্যাটাগরি</div>
                </div>
                <div class="stat-trend">
                    <i class="fas fa-equals"></i>
                    <span>স্থিতিশীল</span>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="search-filter-section">
            <div class="search-container-modern">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input-modern" placeholder="ক্যাটাগরি খুঁজুন..." id="categorySearch">
                    <button class="search-clear-btn" onclick="clearSearch()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="filter-container-modern">
                <select class="filter-select-modern" id="statusFilter">
                    <option value="">সব স্ট্যাটাস</option>
                    <option value="active">সক্রিয়</option>
                    <option value="inactive">নিষ্ক্রিয়</option>
                </select>
                
                <select class="filter-select-modern" id="sortFilter">
                    <option value="name">নাম অনুযায়ী</option>
                    <option value="products_count">পণ্য সংখ্যা</option>
                    <option value="created_at">তারিখ অনুযায়ী</option>
                </select>
                
                <button class="filter-btn-modern" onclick="applyFilters()">
                    <i class="fas fa-filter"></i>
                    ফিল্টার প্রয়োগ
                </button>
            </div>
        </div>

        <!-- Categories Grid -->
        <div class="categories-grid" id="categoriesGrid">
            <?php foreach ($categories as $category): ?>
                <div class="category-card-modern" data-category-id="<?php echo $category['id']; ?>" data-status="<?php echo $category['status']; ?>">
                    <div class="category-image">
                        <div class="image-placeholder">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="category-status <?php echo $category['status']; ?>">
                            <?php if ($category['status'] == 'active'): ?>
                                <i class="fas fa-check-circle"></i>
                                <span>সক্রিয়</span>
                            <?php else: ?>
                                <i class="fas fa-pause-circle"></i>
                                <span>নিষ্ক্রিয়</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="category-content">
                        <h3 class="category-name"><?php echo $category['name']; ?></h3>
                        <p class="category-description"><?php echo $category['description']; ?></p>
                        
                        <div class="category-stats">
                            <div class="stat-item">
                                <i class="fas fa-boxes"></i>
                                <span><?php echo toBengaliNumber($category['products_count']); ?> পণ্য</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-calendar"></i>
                                <span><?php echo formatDate($category['created_at']); ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="category-actions">
                        <button class="action-btn-small primary" onclick="viewCategory(<?php echo $category['id']; ?>)" title="দেখুন">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn-small success" onclick="editCategory(<?php echo $category['id']; ?>)" title="সম্পাদনা">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn-small warning" onclick="toggleStatus(<?php echo $category['id']; ?>)" title="স্ট্যাটাস পরিবর্তন">
                            <i class="fas fa-power-off"></i>
                        </button>
                        <button class="action-btn-small danger" onclick="deleteCategory(<?php echo $category['id']; ?>)" title="মুছুন">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <div class="pagination-modern">
            <button class="pagination-btn prev" disabled>
                <i class="fas fa-chevron-left"></i>
                পূর্ববর্তী
            </button>
            
            <div class="pagination-numbers">
                <button class="pagination-number active">১</button>
                <button class="pagination-number">২</button>
                <button class="pagination-number">৩</button>
                <span class="pagination-dots">...</span>
                <button class="pagination-number">১০</button>
            </div>
            
            <button class="pagination-btn next">
                পরবর্তী
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>
</div>

<!-- Add/Edit Category Modal -->
<div class="modal-overlay" id="categoryModal">
    <div class="modal-container-modern">
        <div class="modal-header">
            <h3 class="modal-title">
                <i class="fas fa-plus-circle"></i>
                নতুন ক্যাটাগরি যোগ করুন
            </h3>
            <button class="modal-close" onclick="closeModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <form id="categoryForm" class="form-modern">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-tag"></i>
                            ক্যাটাগরির নাম *
                        </label>
                        <input type="text" class="form-input" name="name" placeholder="ক্যাটাগরির নাম লিখুন" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-align-left"></i>
                            বিবরণ
                        </label>
                        <textarea class="form-textarea" name="description" placeholder="ক্যাটাগরির বিবরণ লিখুন" rows="3"></textarea>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-image"></i>
                            ছবি
                        </label>
                        <div class="file-upload-area">
                            <input type="file" class="file-input" name="image" accept="image/*">
                            <div class="file-upload-content">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <span>ছবি আপলোড করুন</span>
                                <small>JPG, PNG, GIF (সর্বোচ্চ ২MB)</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-toggle-on"></i>
                            স্ট্যাটাস
                        </label>
                        <div class="toggle-switch">
                            <input type="checkbox" class="toggle-input" name="status" checked>
                            <label class="toggle-label">
                                <span class="toggle-slider"></span>
                                <span class="toggle-text">সক্রিয়</span>
                            </label>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="modal-footer">
            <button class="btn-modern secondary" onclick="closeModal()">
                <i class="fas fa-times"></i>
                বাতিল
            </button>
            <button class="btn-modern primary" onclick="saveCategory()">
                <i class="fas fa-save"></i>
                সংরক্ষণ করুন
            </button>
        </div>
    </div>
</div>

<!-- Categories JavaScript -->
<script>
// Global variables
let currentEditId = null;
let categories = <?php echo json_encode($categories); ?>;

// Initialize page
$(document).ready(function() {
    initializeEventListeners();
    showNotification('ক্যাটাগরি পেজ লোড হয়েছে', 'success', 3000);
});

// Event listeners
function initializeEventListeners() {
    // Search functionality
    $('#categorySearch').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterCategories(searchTerm);
    });

    // Filter functionality
    $('#statusFilter, #sortFilter').on('change', function() {
        applyFilters();
    });

    // File upload preview
    $('.file-input').on('change', function() {
        handleFileUpload(this);
    });

    // Form submission
    $('#categoryForm').on('submit', function(e) {
        e.preventDefault();
        saveCategory();
    });

    // Modal close on overlay click
    $('.modal-overlay').on('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            openAddModal();
        }
    });
}

// Category management functions
function openAddModal() {
    currentEditId = null;
    $('#categoryForm')[0].reset();
    $('.modal-title').html('<i class="fas fa-plus-circle"></i> নতুন ক্যাটাগরি যোগ করুন');
    $('.file-upload-content span').text('ছবি আপলোড করুন');
    $('#categoryModal').addClass('show');
    $('body').css('overflow', 'hidden');

    // Focus on first input
    setTimeout(() => {
        $('input[name="name"]').focus();
    }, 300);
}

function editCategory(id) {
    const category = categories.find(cat => cat.id == id);
    if (!category) {
        showNotification('ক্যাটাগরি খুঁজে পাওয়া যায়নি', 'error');
        return;
    }

    currentEditId = id;

    // Fill form with category data
    $('input[name="name"]').val(category.name);
    $('textarea[name="description"]').val(category.description);
    $('input[name="status"]').prop('checked', category.status === 'active');

    $('.modal-title').html('<i class="fas fa-edit"></i> ক্যাটাগরি সম্পাদনা করুন');
    $('#categoryModal').addClass('show');
    $('body').css('overflow', 'hidden');

    showNotification(`"${category.name}" সম্পাদনার জন্য লোড হয়েছে`, 'info');
}

function viewCategory(id) {
    const category = categories.find(cat => cat.id == id);
    if (!category) {
        showNotification('ক্যাটাগরি খুঁজে পাওয়া যায়নি', 'error');
        return;
    }

    // Create view modal content
    const viewContent = `
        <div class="category-view-modal">
            <div class="view-header">
                <div class="view-image">
                    <i class="fas fa-image"></i>
                </div>
                <div class="view-info">
                    <h2>${category.name}</h2>
                    <p>${category.description}</p>
                    <div class="view-status ${category.status}">
                        <i class="fas fa-${category.status === 'active' ? 'check-circle' : 'pause-circle'}"></i>
                        ${category.status === 'active' ? 'সক্রিয়' : 'নিষ্ক্রিয়'}
                    </div>
                </div>
            </div>
            <div class="view-stats">
                <div class="view-stat">
                    <i class="fas fa-boxes"></i>
                    <span>${toBengaliNumber(category.products_count)} পণ্য</span>
                </div>
                <div class="view-stat">
                    <i class="fas fa-calendar"></i>
                    <span>${formatDate(category.created_at)}</span>
                </div>
            </div>
        </div>
    `;

    // Show in a simple alert for now (can be enhanced with a proper modal)
    showNotification(`ক্যাটাগরি: ${category.name} - ${category.products_count} পণ্য`, 'info', 5000);
}

function toggleStatus(id) {
    const category = categories.find(cat => cat.id == id);
    if (!category) {
        showNotification('ক্যাটাগরি খুঁজে পাওয়া যায়নি', 'error');
        return;
    }

    const newStatus = category.status === 'active' ? 'inactive' : 'active';
    const statusText = newStatus === 'active' ? 'সক্রিয়' : 'নিষ্ক্রিয়';

    if (confirm(`আপনি কি "${category.name}" ক্যাটাগরিটি ${statusText} করতে চান?`)) {
        // Update category status
        category.status = newStatus;

        // Update UI
        const categoryCard = $(`.category-card-modern[data-category-id="${id}"]`);
        categoryCard.attr('data-status', newStatus);

        const statusElement = categoryCard.find('.category-status');
        statusElement.removeClass('active inactive').addClass(newStatus);

        if (newStatus === 'active') {
            statusElement.html('<i class="fas fa-check-circle"></i><span>সক্রিয়</span>');
        } else {
            statusElement.html('<i class="fas fa-pause-circle"></i><span>নিষ্ক্রিয়</span>');
        }

        showNotification(`"${category.name}" ক্যাটাগরি ${statusText} করা হয়েছে`, 'success');

        // Update statistics
        updateStatistics();
    }
}

function deleteCategory(id) {
    const category = categories.find(cat => cat.id == id);
    if (!category) {
        showNotification('ক্যাটাগরি খুঁজে পাওয়া যায়নি', 'error');
        return;
    }

    if (category.products_count > 0) {
        showNotification('এই ক্যাটাগরিতে পণ্য রয়েছে। প্রথমে পণ্যগুলো সরান।', 'warning');
        return;
    }

    if (confirm(`আপনি কি নিশ্চিত যে "${category.name}" ক্যাটাগরিটি মুছে ফেলতে চান?`)) {
        // Remove from array
        categories = categories.filter(cat => cat.id != id);

        // Remove from UI
        $(`.category-card-modern[data-category-id="${id}"]`).fadeOut(300, function() {
            $(this).remove();
            updateStatistics();
        });

        showNotification(`"${category.name}" ক্যাটাগরি মুছে ফেলা হয়েছে`, 'success');
    }
}

function saveCategory() {
    const formData = new FormData($('#categoryForm')[0]);
    const name = formData.get('name').trim();
    const description = formData.get('description').trim();
    const status = formData.get('status') ? 'active' : 'inactive';

    // Validation
    if (!name) {
        showNotification('ক্যাটাগরির নাম আবশ্যক', 'error');
        $('input[name="name"]').focus();
        return;
    }

    // Check for duplicate name (excluding current edit)
    const existingCategory = categories.find(cat =>
        cat.name.toLowerCase() === name.toLowerCase() &&
        cat.id != currentEditId
    );

    if (existingCategory) {
        showNotification('এই নামের ক্যাটাগরি ইতিমধ্যে রয়েছে', 'error');
        $('input[name="name"]').focus();
        return;
    }

    // Show loading
    const saveBtn = $('.btn-modern.primary');
    const originalText = saveBtn.html();
    saveBtn.html('<i class="fas fa-spinner fa-spin"></i> সংরক্ষণ হচ্ছে...');
    saveBtn.prop('disabled', true);

    // Simulate API call
    setTimeout(() => {
        if (currentEditId) {
            // Update existing category
            const category = categories.find(cat => cat.id == currentEditId);
            category.name = name;
            category.description = description;
            category.status = status;

            // Update UI
            updateCategoryCard(currentEditId, category);
            showNotification(`"${name}" ক্যাটাগরি আপডেট হয়েছে`, 'success');
        } else {
            // Add new category
            const newCategory = {
                id: Date.now(),
                name: name,
                description: description,
                products_count: 0,
                status: status,
                created_at: new Date().toISOString().split('T')[0],
                image: 'default.jpg'
            };

            categories.push(newCategory);
            addCategoryCard(newCategory);
            showNotification(`"${name}" ক্যাটাগরি যোগ করা হয়েছে`, 'success');
        }

        // Reset form and close modal
        saveBtn.html(originalText);
        saveBtn.prop('disabled', false);
        closeModal();
        updateStatistics();

    }, 1500);
}

function closeModal() {
    $('#categoryModal').removeClass('show');
    $('body').css('overflow', 'auto');
    currentEditId = null;
}

// UI update functions
function updateCategoryCard(id, category) {
    const card = $(`.category-card-modern[data-category-id="${id}"]`);
    card.find('.category-name').text(category.name);
    card.find('.category-description').text(category.description);
    card.attr('data-status', category.status);

    const statusElement = card.find('.category-status');
    statusElement.removeClass('active inactive').addClass(category.status);

    if (category.status === 'active') {
        statusElement.html('<i class="fas fa-check-circle"></i><span>সক্রিয়</span>');
    } else {
        statusElement.html('<i class="fas fa-pause-circle"></i><span>নিষ্ক্রিয়</span>');
    }
}

function addCategoryCard(category) {
    const cardHtml = `
        <div class="category-card-modern" data-category-id="${category.id}" data-status="${category.status}" style="display: none;">
            <div class="category-image">
                <div class="image-placeholder">
                    <i class="fas fa-image"></i>
                </div>
                <div class="category-status ${category.status}">
                    <i class="fas fa-${category.status === 'active' ? 'check-circle' : 'pause-circle'}"></i>
                    <span>${category.status === 'active' ? 'সক্রিয়' : 'নিষ্ক্রিয়'}</span>
                </div>
            </div>

            <div class="category-content">
                <h3 class="category-name">${category.name}</h3>
                <p class="category-description">${category.description}</p>

                <div class="category-stats">
                    <div class="stat-item">
                        <i class="fas fa-boxes"></i>
                        <span>${toBengaliNumber(category.products_count)} পণ্য</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-calendar"></i>
                        <span>${formatDate(category.created_at)}</span>
                    </div>
                </div>
            </div>

            <div class="category-actions">
                <button class="action-btn-small primary" onclick="viewCategory(${category.id})" title="দেখুন">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn-small success" onclick="editCategory(${category.id})" title="সম্পাদনা">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn-small warning" onclick="toggleStatus(${category.id})" title="স্ট্যাটাস পরিবর্তন">
                    <i class="fas fa-power-off"></i>
                </button>
                <button class="action-btn-small danger" onclick="deleteCategory(${category.id})" title="মুছুন">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;

    $('#categoriesGrid').append(cardHtml);
    $(`.category-card-modern[data-category-id="${category.id}"]`).fadeIn(300);
}

function updateStatistics() {
    const totalCategories = categories.length;
    const activeCategories = categories.filter(cat => cat.status === 'active').length;
    const totalProducts = categories.reduce((sum, cat) => sum + cat.products_count, 0);
    const avgProducts = totalCategories > 0 ? Math.round(totalProducts / totalCategories) : 0;

    $('.stat-card-modern.primary .stat-number').text(toBengaliNumber(totalCategories));
    $('.stat-card-modern.success .stat-number').text(toBengaliNumber(activeCategories));
    $('.stat-card-modern.info .stat-number').text(toBengaliNumber(totalProducts));
    $('.stat-card-modern.warning .stat-number').text(toBengaliNumber(avgProducts));
}

// Search and filter functions
function filterCategories(searchTerm) {
    $('.category-card-modern').each(function() {
        const categoryName = $(this).find('.category-name').text().toLowerCase();
        const categoryDesc = $(this).find('.category-description').text().toLowerCase();

        if (categoryName.includes(searchTerm) || categoryDesc.includes(searchTerm)) {
            $(this).fadeIn(200);
        } else {
            $(this).fadeOut(200);
        }
    });
}

function clearSearch() {
    $('#categorySearch').val('');
    $('.category-card-modern').fadeIn(200);
}

function applyFilters() {
    const statusFilter = $('#statusFilter').val();
    const sortFilter = $('#sortFilter').val();

    // Filter by status
    $('.category-card-modern').each(function() {
        const cardStatus = $(this).attr('data-status');

        if (!statusFilter || cardStatus === statusFilter) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });

    // Sort categories
    if (sortFilter) {
        sortCategories(sortFilter);
    }

    showNotification('ফিল্টার প্রয়োগ করা হয়েছে', 'info');
}

function sortCategories(sortBy) {
    const grid = $('#categoriesGrid');
    const cards = grid.children('.category-card-modern').get();

    cards.sort(function(a, b) {
        const aId = $(a).attr('data-category-id');
        const bId = $(b).attr('data-category-id');
        const aCat = categories.find(cat => cat.id == aId);
        const bCat = categories.find(cat => cat.id == bId);

        switch(sortBy) {
            case 'name':
                return aCat.name.localeCompare(bCat.name);
            case 'products_count':
                return bCat.products_count - aCat.products_count;
            case 'created_at':
                return new Date(bCat.created_at) - new Date(aCat.created_at);
            default:
                return 0;
        }
    });

    $.each(cards, function(index, card) {
        grid.append(card);
    });
}

// Utility functions
function handleFileUpload(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];
        const maxSize = 2 * 1024 * 1024; // 2MB

        if (file.size > maxSize) {
            showNotification('ফাইলের সাইজ ২MB এর বেশি হতে পারবে না', 'error');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            $('.file-upload-content span').text(file.name);
            showNotification('ছবি সফলভাবে নির্বাচিত হয়েছে', 'success');
        };
        reader.readAsDataURL(file);
    }
}

function exportCategories() {
    const csvContent = "data:text/csv;charset=utf-8,"
        + "ID,Name,Description,Products Count,Status,Created At\n"
        + categories.map(cat =>
            `${cat.id},"${cat.name}","${cat.description}",${cat.products_count},${cat.status},${cat.created_at}`
        ).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "categories.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification('ক্যাটাগরি তালিকা এক্সপোর্ট হয়েছে', 'success');
}

function importCategories() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            showNotification('ইমপোর্ট ফিচার শীঘ্রই আসছে', 'info');
        }
    };
    input.click();
}

// Helper functions
function toBengaliNumber(num) {
    const bengaliDigits = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
    return num.toString().replace(/\d/g, digit => bengaliDigits[digit]);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    const day = toBengaliNumber(date.getDate());
    const month = toBengaliNumber(date.getMonth() + 1);
    const year = toBengaliNumber(date.getFullYear());
    return `${day}/${month}/${year}`;
}
</script>

<?php include '../includes/footer.php'; ?>
