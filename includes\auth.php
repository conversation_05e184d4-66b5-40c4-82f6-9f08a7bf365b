<?php
// অথেনটিকেশন ফাংশন
// Authentication Functions

// Login function
function login($username, $password) {
    $db = new Database();
    $conn = $db->getConnection();
    
    $query = "SELECT * FROM users WHERE username = :username AND status = 'active'";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['role'] = $user['role'];
        
        // Log login activity
        logActivity('User Login', 'User logged in successfully');
        
        return true;
    }
    
    return false;
}

// Logout function
function logout() {
    // Log logout activity
    logActivity('User Logout', 'User logged out');
    
    // Destroy session
    session_destroy();
    
    // Redirect to login page
    redirect(BASE_URL . 'login.php');
}

// Check if user is authenticated
function requireAuth() {
    if (!isLoggedIn()) {
        redirect(BASE_URL . 'login.php');
    }
}

// Check if user has required role
function requireRole($required_role) {
    requireAuth();
    
    if (!hasPermission($required_role)) {
        showAlert('আপনার এই পেজ অ্যাক্সেস করার অনুমতি নেই।', 'error');
        redirect(BASE_URL . 'dashboard.php');
    }
}

// Register new user (admin only)
function registerUser($data) {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Check if username already exists
    $query = "SELECT id FROM users WHERE username = :username";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':username', $data['username']);
    $stmt->execute();
    
    if ($stmt->fetch()) {
        return ['success' => false, 'message' => 'এই ইউজারনেম ইতিমধ্যে ব্যবহৃত হয়েছে।'];
    }
    
    // Hash password
    $hashed_password = password_hash($data['password'], PASSWORD_DEFAULT);
    
    // Insert new user
    $query = "INSERT INTO users (username, password, full_name, email, phone, role) 
              VALUES (:username, :password, :full_name, :email, :phone, :role)";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':username', $data['username']);
    $stmt->bindParam(':password', $hashed_password);
    $stmt->bindParam(':full_name', $data['full_name']);
    $stmt->bindParam(':email', $data['email']);
    $stmt->bindParam(':phone', $data['phone']);
    $stmt->bindParam(':role', $data['role']);
    
    if ($stmt->execute()) {
        logActivity('User Registration', 'New user registered: ' . $data['username']);
        return ['success' => true, 'message' => 'নতুন ইউজার সফলভাবে তৈরি হয়েছে।'];
    } else {
        return ['success' => false, 'message' => 'ইউজার তৈরি করতে সমস্যা হয়েছে।'];
    }
}

// Change password
function changePassword($user_id, $old_password, $new_password) {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get current password
    $query = "SELECT password FROM users WHERE id = :user_id";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    
    $user = $stmt->fetch();
    
    if (!$user || !password_verify($old_password, $user['password'])) {
        return ['success' => false, 'message' => 'পুরাতন পাসওয়ার্ড সঠিক নয়।'];
    }
    
    // Update password
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    
    $query = "UPDATE users SET password = :password WHERE id = :user_id";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':password', $hashed_password);
    $stmt->bindParam(':user_id', $user_id);
    
    if ($stmt->execute()) {
        logActivity('Password Change', 'User changed password');
        return ['success' => true, 'message' => 'পাসওয়ার্ড সফলভাবে পরিবর্তন হয়েছে।'];
    } else {
        return ['success' => false, 'message' => 'পাসওয়ার্ড পরিবর্তন করতে সমস্যা হয়েছে।'];
    }
}

// Update user profile
function updateProfile($user_id, $data) {
    $db = new Database();
    $conn = $db->getConnection();
    
    $query = "UPDATE users SET full_name = :full_name, email = :email, phone = :phone WHERE id = :user_id";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':full_name', $data['full_name']);
    $stmt->bindParam(':email', $data['email']);
    $stmt->bindParam(':phone', $data['phone']);
    $stmt->bindParam(':user_id', $user_id);
    
    if ($stmt->execute()) {
        // Update session data
        $_SESSION['full_name'] = $data['full_name'];
        
        logActivity('Profile Update', 'User updated profile');
        return ['success' => true, 'message' => 'প্রোফাইল সফলভাবে আপডেট হয়েছে।'];
    } else {
        return ['success' => false, 'message' => 'প্রোফাইল আপডেট করতে সমস্যা হয়েছে।'];
    }
}
?>
