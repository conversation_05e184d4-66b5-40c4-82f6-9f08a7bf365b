<?php
require_once '../config/config.php';
requireAuth();

$page_title = 'পণ্য তালিকা';

$db = new Database();
$conn = $db->getConnection();

// Handle search and filters
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';

// Build query
$where_conditions = ["p.status != 'deleted'"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(p.name LIKE :search OR p.barcode LIKE :search)";
    $params[':search'] = "%$search%";
}

if ($category_filter > 0) {
    $where_conditions[] = "p.category_id = :category_id";
    $params[':category_id'] = $category_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = "p.status = :status";
    $params[':status'] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// Get products
$query = "SELECT p.*, c.name as category_name 
          FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          WHERE $where_clause 
          ORDER BY p.name ASC";

$stmt = $conn->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$products = $stmt->fetchAll();

// Get categories for filter
$query = "SELECT * FROM categories WHERE status = 'active' ORDER BY name";
$stmt = $conn->prepare($query);
$stmt->execute();
$categories = $stmt->fetchAll();

include '../includes/header.php';
?>

<div class="main-content">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-box"></i> পণ্য তালিকা</h2>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> নতুন পণ্য যোগ করুন
            </a>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" 
                               placeholder="পণ্যের নাম বা বারকোড দিয়ে খুঁজুন..." 
                               value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="col-md-3">
                        <select name="category" class="form-control">
                            <option value="">সকল ক্যাটাগরি</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>" 
                                        <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                    <?php echo $category['name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-control">
                            <option value="">সকল স্ট্যাটাস</option>
                            <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>সক্রিয়</option>
                            <option value="inactive" <?php echo $status_filter == 'inactive' ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-secondary">
                            <i class="fas fa-search"></i> খুঁজুন
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Products Table -->
        <div class="card">
            <div class="card-header">
                <h5>পণ্য তালিকা (<?php echo toBengaliNumber(count($products)); ?>টি পণ্য)</h5>
            </div>
            <div class="card-body">
                <?php if (empty($products)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-box fa-3x text-muted mb-3"></i>
                        <p class="text-muted">কোন পণ্য পাওয়া যায়নি।</p>
                        <a href="add.php" class="btn btn-primary">প্রথম পণ্য যোগ করুন</a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="products-table">
                            <thead>
                                <tr>
                                    <th>নাম</th>
                                    <th>ক্যাটাগরি</th>
                                    <th>বারকোড</th>
                                    <th>ক্রয় মূল্য</th>
                                    <th>বিক্রয় মূল্য</th>
                                    <th>স্টক</th>
                                    <th>মেয়াদ</th>
                                    <th>স্ট্যাটাস</th>
                                    <th>কার্যক্রম</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($products as $product): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $product['name']; ?></strong>
                                            <?php if ($product['description']): ?>
                                                <br><small class="text-muted"><?php echo substr($product['description'], 0, 50); ?>...</small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $product['category_name'] ?? 'N/A'; ?></td>
                                        <td><?php echo $product['barcode'] ?? 'N/A'; ?></td>
                                        <td><?php echo formatCurrency($product['purchase_price']); ?></td>
                                        <td><?php echo formatCurrency($product['selling_price']); ?></td>
                                        <td>
                                            <span class="<?php echo $product['stock_quantity'] <= $product['min_stock_level'] ? 'text-danger' : ''; ?>">
                                                <?php echo toBengaliNumber($product['stock_quantity']); ?> <?php echo $product['unit']; ?>
                                            </span>
                                            <?php if ($product['stock_quantity'] <= $product['min_stock_level']): ?>
                                                <i class="fas fa-exclamation-triangle text-warning" title="কম স্টক"></i>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($product['expiry_date']): ?>
                                                <?php 
                                                $expiry_date = new DateTime($product['expiry_date']);
                                                $today = new DateTime();
                                                $diff = $today->diff($expiry_date);
                                                ?>
                                                <?php if ($expiry_date < $today): ?>
                                                    <span class="text-danger">
                                                        <?php echo formatDate($product['expiry_date']); ?>
                                                        <i class="fas fa-exclamation-circle" title="মেয়াদ শেষ"></i>
                                                    </span>
                                                <?php elseif ($diff->days <= 30): ?>
                                                    <span class="text-warning">
                                                        <?php echo formatDate($product['expiry_date']); ?>
                                                        <i class="fas fa-exclamation-triangle" title="শীঘ্রই মেয়াদ শেষ"></i>
                                                    </span>
                                                <?php else: ?>
                                                    <?php echo formatDate($product['expiry_date']); ?>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                N/A
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($product['status'] == 'active'): ?>
                                                <span class="badge badge-success">সক্রিয়</span>
                                            <?php else: ?>
                                                <span class="badge badge-secondary">নিষ্ক্রিয়</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="view.php?id=<?php echo $product['id']; ?>" 
                                                   class="btn btn-info" title="দেখুন">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit.php?id=<?php echo $product['id']; ?>" 
                                                   class="btn btn-warning" title="সম্পাদনা">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="delete.php?id=<?php echo $product['id']; ?>" 
                                                   class="btn btn-danger btn-delete" title="মুছুন">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable if available
    if (typeof $.fn.DataTable !== 'undefined') {
        $('#products-table').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Bengali.json"
            },
            "pageLength": 25,
            "order": [[0, "asc"]]
        });
    }
});
</script>

<?php include '../includes/footer.php'; ?>
