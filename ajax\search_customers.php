<?php
require_once '../config/config.php';
requireAuth();

$db = new Database();
$conn = $db->getConnection();

$term = isset($_GET['term']) ? sanitize($_GET['term']) : '';
$results = [];

if (!empty($term)) {
    $query = "SELECT id, name, phone, address, balance 
              FROM customers 
              WHERE (name LIKE :term OR phone LIKE :term) 
              AND status = 'active' 
              ORDER BY name 
              LIMIT 10";
    
    $stmt = $conn->prepare($query);
    $stmt->bindValue(':term', "%$term%");
    $stmt->execute();
    
    $customers = $stmt->fetchAll();
    
    foreach ($customers as $customer) {
        $label = $customer['name'];
        if ($customer['phone']) {
            $label .= ' (' . $customer['phone'] . ')';
        }
        if ($customer['balance'] > 0) {
            $label .= ' - বকেয়া: ৳' . number_format($customer['balance'], 2);
        }
        
        $results[] = [
            'id' => $customer['id'],
            'label' => $label,
            'value' => $customer['name'],
            'name' => $customer['name'],
            'phone' => $customer['phone'],
            'address' => $customer['address'],
            'balance' => $customer['balance']
        ];
    }
}

header('Content-Type: application/json');
echo json_encode($results);
?>
