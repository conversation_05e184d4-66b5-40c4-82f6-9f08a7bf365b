<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . APP_NAME : APP_NAME; ?></title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- jQuery UI for autocomplete -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">
    
    <style>
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -15px;
        }
        
        .col-md-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
            padding: 0 15px;
        }
        
        .col-md-8 {
            flex: 0 0 66.666667%;
            max-width: 66.666667%;
            padding: 0 15px;
        }
        
        .col-md-6 {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 0 15px;
        }
        
        .col-md-12 {
            flex: 0 0 100%;
            max-width: 100%;
            padding: 0 15px;
        }
        
        .d-flex {
            display: flex;
        }
        
        .justify-content-between {
            justify-content: space-between;
        }
        
        .align-items-center {
            align-items: center;
        }
        
        .d-grid {
            display: grid;
        }
        
        .gap-2 {
            gap: 0.5rem;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25em 0.4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }
        
        .badge-success {
            color: #fff;
            background-color: #28a745;
        }
        
        .badge-warning {
            color: #212529;
            background-color: #ffc107;
        }
        
        .badge-danger {
            color: #fff;
            background-color: #dc3545;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        .alert-link {
            font-weight: 700;
            color: inherit;
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .col-md-4, .col-md-6, .col-md-8, .col-md-12 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }

        /* Enhanced header styles */
        .header {
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            pointer-events: none;
        }

        .header .container-fluid {
            position: relative;
            z-index: 1;
        }

        .user-info {
            background: rgba(255,255,255,0.1);
            padding: 8px 15px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .user-info:hover {
            background: rgba(255,255,255,0.15);
            transform: translateY(-1px);
        }

        .user-info a {
            transition: color 0.3s ease;
        }

        .user-info a:hover {
            color: #ffd700 !important;
        }

        .mx-2 {
            margin-left: 0.5rem;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <h1><?php echo APP_NAME; ?></h1>
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    স্বাগতম, <?php echo getCurrentUser()['full_name']; ?>
                    <span class="mx-2">|</span>
                    <a href="logout.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-sign-out-alt"></i> লগআউট
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container-fluid">
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul id="navbar-menu">
                <li><a href="<?php echo BASE_URL; ?>dashboard.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                </a></li>
                
                <li class="dropdown">
                    <a href="#" class="<?php echo strpos($_SERVER['PHP_SELF'], '/products/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-box"></i> পণ্য ব্যবস্থাপনা <i class="fas fa-chevron-down"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a href="<?php echo BASE_URL; ?>products/index.php"><i class="fas fa-list"></i>সকল পণ্য</a></li>
                        <li><a href="<?php echo BASE_URL; ?>products/add.php"><i class="fas fa-plus-circle"></i>নতুন পণ্য</a></li>
                        <li><a href="<?php echo BASE_URL; ?>categories/index.php"><i class="fas fa-tags"></i>ক্যাটাগরি</a></li>
                    </ul>
                </li>
                
                <li class="dropdown">
                    <a href="#" class="<?php echo strpos($_SERVER['PHP_SELF'], '/sales/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-shopping-cart"></i> বিক্রয় <i class="fas fa-chevron-down"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a href="<?php echo BASE_URL; ?>sales/add.php"><i class="fas fa-plus-circle"></i>নতুন বিক্রয়</a></li>
                        <li><a href="<?php echo BASE_URL; ?>sales/index.php"><i class="fas fa-list"></i>বিক্রয় তালিকা</a></li>
                    </ul>
                </li>
                
                <li class="dropdown">
                    <a href="#" class="<?php echo strpos($_SERVER['PHP_SELF'], '/purchases/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-truck"></i> ক্রয় <i class="fas fa-chevron-down"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a href="<?php echo BASE_URL; ?>purchases/add.php"><i class="fas fa-plus-circle"></i>নতুন ক্রয়</a></li>
                        <li><a href="<?php echo BASE_URL; ?>purchases/index.php"><i class="fas fa-list"></i>ক্রয় তালিকা</a></li>
                    </ul>
                </li>
                
                <li class="dropdown">
                    <a href="#" class="<?php echo strpos($_SERVER['PHP_SELF'], '/customers/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-users"></i> কাস্টমার <i class="fas fa-chevron-down"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a href="<?php echo BASE_URL; ?>customers/index.php"><i class="fas fa-list"></i>কাস্টমার তালিকা</a></li>
                        <li><a href="<?php echo BASE_URL; ?>customers/add.php"><i class="fas fa-user-plus"></i>নতুন কাস্টমার</a></li>
                    </ul>
                </li>
                
                <li class="dropdown">
                    <a href="#" class="<?php echo strpos($_SERVER['PHP_SELF'], '/suppliers/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-industry"></i> সাপ্লায়ার <i class="fas fa-chevron-down"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a href="<?php echo BASE_URL; ?>suppliers/index.php"><i class="fas fa-list"></i>সাপ্লায়ার তালিকা</a></li>
                        <li><a href="<?php echo BASE_URL; ?>suppliers/add.php"><i class="fas fa-plus-circle"></i>নতুন সাপ্লায়ার</a></li>
                    </ul>
                </li>
                
                <li class="dropdown">
                    <a href="#" class="<?php echo strpos($_SERVER['PHP_SELF'], '/reports/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-chart-bar"></i> রিপোর্ট <i class="fas fa-chevron-down"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a href="<?php echo BASE_URL; ?>reports/sales.php"><i class="fas fa-chart-line"></i>বিক্রয় রিপোর্ট</a></li>
                        <li><a href="<?php echo BASE_URL; ?>reports/purchases.php"><i class="fas fa-chart-area"></i>ক্রয় রিপোর্ট</a></li>
                        <li><a href="<?php echo BASE_URL; ?>reports/stock.php"><i class="fas fa-boxes"></i>স্টক রিপোর্ট</a></li>
                        <li><a href="<?php echo BASE_URL; ?>reports/profit_loss.php"><i class="fas fa-calculator"></i>লাভ-ক্ষতি রিপোর্ট</a></li>
                        <li><a href="<?php echo BASE_URL; ?>reports/customer_dues.php"><i class="fas fa-user-clock"></i>কাস্টমার বকেয়া</a></li>
                        <li><a href="<?php echo BASE_URL; ?>reports/supplier_dues.php"><i class="fas fa-industry"></i>সাপ্লায়ার বকেয়া</a></li>
                    </ul>
                </li>
                
                <?php if (hasPermission('admin')): ?>
                <li class="dropdown">
                    <a href="#" class="<?php echo strpos($_SERVER['PHP_SELF'], '/admin/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-cog"></i> সেটিংস <i class="fas fa-chevron-down"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a href="<?php echo BASE_URL; ?>admin/users.php"><i class="fas fa-users-cog"></i>ইউজার ব্যবস্থাপনা</a></li>
                        <li><a href="<?php echo BASE_URL; ?>admin/backup.php"><i class="fas fa-database"></i>ব্যাকআপ</a></li>
                        <li><a href="<?php echo BASE_URL; ?>admin/settings.php"><i class="fas fa-sliders-h"></i>সিস্টেম সেটিংস</a></li>
                    </ul>
                </li>
                <?php endif; ?>
            </ul>
        </div>
    </nav>

    <!-- Loading Screen -->
    <div id="loading-screen" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 9999; justify-content: center; align-items: center;">
        <div style="text-align: center; color: white;">
            <div style="width: 50px; height: 50px; border: 3px solid rgba(255,255,255,0.3); border-top: 3px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <p style="font-size: 16px; margin: 0;">লোড হচ্ছে...</p>
        </div>
    </div>

    <!-- Breadcrumb Navigation -->
    <?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
    <nav class="breadcrumb">
        <ol class="breadcrumb-list">
            <li class="breadcrumb-item">
                <a href="<?php echo BASE_URL; ?>dashboard.php">
                    <i class="fas fa-home"></i> হোম
                </a>
            </li>
            <?php foreach ($breadcrumbs as $breadcrumb): ?>
                <?php if (isset($breadcrumb['url'])): ?>
                    <li class="breadcrumb-item">
                        <a href="<?php echo $breadcrumb['url']; ?>">
                            <?php if (isset($breadcrumb['icon'])): ?>
                                <i class="<?php echo $breadcrumb['icon']; ?>"></i>
                            <?php endif; ?>
                            <?php echo $breadcrumb['title']; ?>
                        </a>
                    </li>
                <?php else: ?>
                    <li class="breadcrumb-item active">
                        <?php if (isset($breadcrumb['icon'])): ?>
                            <i class="<?php echo $breadcrumb['icon']; ?>"></i>
                        <?php endif; ?>
                        <?php echo $breadcrumb['title']; ?>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </nav>
    <?php endif; ?>

    <!-- Alert Messages -->
    <div id="alerts-container">
        <?php
        $alert = getAlert();
        if ($alert):
        ?>
            <div class="container-fluid mt-3">
                <div class="alert alert-<?php echo $alert['type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $alert['message']; ?>
                    <button type="button" class="close" onclick="this.parentElement.style.display='none';" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Scroll to Top Button -->
    <button class="scroll-to-top" id="scrollToTop" title="উপরে যান">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Loading Spinner -->
    <div id="loading" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999;">
        <div class="spinner"></div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
