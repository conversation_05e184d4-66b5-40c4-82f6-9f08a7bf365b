/* Responsive Fix CSS - সব overflow এবং layout সমস্যা সমাধান */

/* Global Box Model Fix */
* {
    box-sizing: border-box;
}

html, body {
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
}

/* Container Fixes */
.container,
.container-fluid {
    width: 100%;
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
    margin-left: auto;
    margin-right: auto;
    overflow-x: hidden;
}

/* Dashboard Container Fix */
.dashboard-container {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
    box-sizing: border-box;
}

.dashboard-container .container-fluid {
    width: 100%;
    max-width: 100%;
    padding: 0 15px;
}

/* Grid System Fix */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-left: -15px;
    margin-right: -15px;
    width: calc(100% + 30px);
    max-width: calc(100% + 30px);
}

[class*="col-"] {
    position: relative;
    width: 100%;
    padding-left: 15px;
    padding-right: 15px;
    box-sizing: border-box;
}

/* Stats Grid Responsive Fix */
.stats-grid {
    display: grid;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

/* Responsive Grid Columns */
@media (min-width: 1400px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 25px;
    }
}

@media (min-width: 1200px) and (max-width: 1399px) {
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 18px;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }
}

@media (max-width: 767px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .container-fluid {
        padding-left: 8px;
        padding-right: 8px;
    }
    
    .row {
        margin-left: -8px;
        margin-right: -8px;
        width: calc(100% + 16px);
    }
    
    [class*="col-"] {
        padding-left: 8px;
        padding-right: 8px;
    }
}

@media (max-width: 480px) {
    .container-fluid {
        padding-left: 5px;
        padding-right: 5px;
    }
    
    .row {
        margin-left: -5px;
        margin-right: -5px;
        width: calc(100% + 10px);
    }
    
    [class*="col-"] {
        padding-left: 5px;
        padding-right: 5px;
    }
    
    .stats-grid {
        gap: 8px;
    }
}

/* Card Responsive Fix */
.stat-card,
.dashboard-card,
.quick-action-card {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

/* Quick Actions Grid Fix */
.quick-actions-grid {
    display: grid;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

@media (min-width: 1200px) {
    .quick-actions-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 15px;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .quick-actions-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .quick-actions-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
}

@media (max-width: 575px) {
    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }
}

/* Table Responsive Fix */
.modern-table {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
    box-sizing: border-box;
}

.modern-table table {
    width: 100%;
    min-width: 600px;
}

/* Chart Container Fix */
.chart-container {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

/* Alert Container Fix */
.alerts-container {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* Navbar Responsive Fix */
.navbar .container-fluid {
    width: 100%;
    max-width: 100%;
    overflow: visible;
}

/* Mobile Menu Fix */
@media (max-width: 768px) {
    .navbar ul {
        width: 100%;
        max-width: 100%;
        left: 0;
        right: 0;
    }
}

/* Utility Classes */
.w-100 {
    width: 100% !important;
    max-width: 100% !important;
}

.overflow-hidden {
    overflow: hidden !important;
}

.overflow-x-hidden {
    overflow-x: hidden !important;
}

.no-gutters {
    margin-left: 0;
    margin-right: 0;
}

.no-gutters > [class*="col-"] {
    padding-left: 0;
    padding-right: 0;
}

/* Debug Helper (remove in production) */
.debug-border {
    border: 1px solid red !important;
}

.debug-overflow {
    overflow: visible !important;
    border: 2px dashed blue !important;
}

/* Print Styles */
@media print {
    .dashboard-container,
    .stats-grid,
    .quick-actions-grid {
        width: 100% !important;
        max-width: 100% !important;
        overflow: visible !important;
    }
}
