<?php
// E<PERSON><PERSON> Handler for Dokan Management System

/**
 * Check if function exists before declaring
 */
function checkFunctionExists($function_name, $file_path = '') {
    if (function_exists($function_name)) {
        error_log("Warning: Function '{$function_name}' already exists" . ($file_path ? " in {$file_path}" : ""));
        return true;
    }
    return false;
}

/**
 * Safe function declaration
 */
function safeDeclareFunction($function_name, $callback) {
    if (!function_exists($function_name)) {
        return $callback();
    }
    return false;
}

/**
 * Display user-friendly error message
 */
function showUserError($message, $type = 'error') {
    $error_types = [
        'error' => ['class' => 'alert-danger', 'icon' => 'fas fa-exclamation-triangle'],
        'warning' => ['class' => 'alert-warning', 'icon' => 'fas fa-exclamation-circle'],
        'info' => ['class' => 'alert-info', 'icon' => 'fas fa-info-circle'],
        'success' => ['class' => 'alert-success', 'icon' => 'fas fa-check-circle']
    ];
    
    $config = $error_types[$type] ?? $error_types['error'];
    
    echo '<div class="alert ' . $config['class'] . ' alert-dismissible fade show" role="alert">';
    echo '<i class="' . $config['icon'] . '"></i> ';
    echo htmlspecialchars($message, ENT_QUOTES, 'UTF-8');
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
    echo '</div>';
}

/**
 * Log error to file
 */
function logError($error_message, $file = '', $line = '') {
    $log_file = __DIR__ . '/../logs/error.log';
    $log_dir = dirname($log_file);
    
    // Create logs directory if it doesn't exist
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] ERROR: {$error_message}";
    
    if ($file) {
        $log_entry .= " in {$file}";
    }
    
    if ($line) {
        $log_entry .= " on line {$line}";
    }
    
    $log_entry .= "\n";
    
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

/**
 * Custom error handler
 */
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    // Don't handle errors that are suppressed with @
    if (!(error_reporting() & $errno)) {
        return false;
    }
    
    $error_types = [
        E_ERROR => 'Fatal Error',
        E_WARNING => 'Warning',
        E_PARSE => 'Parse Error',
        E_NOTICE => 'Notice',
        E_CORE_ERROR => 'Core Error',
        E_CORE_WARNING => 'Core Warning',
        E_COMPILE_ERROR => 'Compile Error',
        E_COMPILE_WARNING => 'Compile Warning',
        E_USER_ERROR => 'User Error',
        E_USER_WARNING => 'User Warning',
        E_USER_NOTICE => 'User Notice',
        E_STRICT => 'Strict Notice',
        E_RECOVERABLE_ERROR => 'Recoverable Error',
        E_DEPRECATED => 'Deprecated',
        E_USER_DEPRECATED => 'User Deprecated'
    ];
    
    $error_type = $error_types[$errno] ?? 'Unknown Error';
    $error_message = "{$error_type}: {$errstr}";
    
    // Log the error
    logError($error_message, $errfile, $errline);
    
    // For fatal errors, show user-friendly message
    if ($errno === E_ERROR || $errno === E_CORE_ERROR || $errno === E_COMPILE_ERROR) {
        if (!headers_sent()) {
            http_response_code(500);
        }
        
        echo '<!DOCTYPE html>';
        echo '<html lang="bn">';
        echo '<head>';
        echo '<meta charset="UTF-8">';
        echo '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
        echo '<title>সিস্টেম ত্রুটি</title>';
        echo '<style>';
        echo 'body { font-family: "Hind Siliguri", Arial, sans-serif; background: #f8f9fa; padding: 50px; text-align: center; }';
        echo '.error-container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }';
        echo '.error-icon { font-size: 4rem; color: #e74c3c; margin-bottom: 20px; }';
        echo '.error-title { font-size: 2rem; color: #2c3e50; margin-bottom: 15px; }';
        echo '.error-message { color: #7f8c8d; margin-bottom: 30px; }';
        echo '.error-actions a { display: inline-block; padding: 12px 24px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; margin: 0 10px; }';
        echo '</style>';
        echo '</head>';
        echo '<body>';
        echo '<div class="error-container">';
        echo '<div class="error-icon">⚠️</div>';
        echo '<h1 class="error-title">সিস্টেম ত্রুটি</h1>';
        echo '<p class="error-message">দুঃখিত, একটি প্রযুক্তিগত সমস্যা হয়েছে। অনুগ্রহ করে পরে আবার চেষ্টা করুন।</p>';
        echo '<div class="error-actions">';
        echo '<a href="javascript:history.back()">পূর্ববর্তী পেজ</a>';
        echo '<a href="' . (defined('BASE_URL') ? BASE_URL : '/') . '">হোম পেজ</a>';
        echo '</div>';
        echo '</div>';
        echo '</body>';
        echo '</html>';
        exit;
    }
    
    return true;
}

/**
 * Custom exception handler
 */
function customExceptionHandler($exception) {
    $error_message = "Uncaught Exception: " . $exception->getMessage();
    logError($error_message, $exception->getFile(), $exception->getLine());
    
    if (!headers_sent()) {
        http_response_code(500);
    }
    
    echo '<!DOCTYPE html>';
    echo '<html lang="bn">';
    echo '<head>';
    echo '<meta charset="UTF-8">';
    echo '<title>সিস্টেম ত্রুটি</title>';
    echo '<style>body { font-family: "Hind Siliguri", Arial, sans-serif; background: #f8f9fa; padding: 50px; text-align: center; }</style>';
    echo '</head>';
    echo '<body>';
    echo '<h1>সিস্টেম ত্রুটি</h1>';
    echo '<p>একটি অপ্রত্যাশিত ত্রুটি হয়েছে। অনুগ্রহ করে পরে আবার চেষ্টা করুন।</p>';
    echo '<a href="javascript:history.back()">পূর্ববর্তী পেজে ফিরুন</a>';
    echo '</body>';
    echo '</html>';
}

// Set custom error and exception handlers
set_error_handler('customErrorHandler');
set_exception_handler('customExceptionHandler');

// Set error reporting level
if (defined('DEBUG') && DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);
    ini_set('display_errors', 0);
}
?>
