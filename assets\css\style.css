/* দোকান ম্যানেজমেন্ট সিস্টেম স্টাইল */
/* Shop Management System Styles */

/* Import Bengali Font */
@import url('https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Hind Siliguri', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.container-fluid {
    width: 100%;
    padding: 0 15px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0;
}

.header .user-info {
    text-align: right;
    font-size: 0.9rem;
}

/* Ultra Modern Navigation */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    padding: 0;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 50%, rgba(240, 147, 251, 0.95) 100%);
    backdrop-filter: blur(30px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3);
    transform: translateY(0);
}

.navbar.navbar-hidden {
    transform: translateY(-100%);
    transition: transform 0.3s ease-in-out;
}

/* Menu item animations */
@keyframes menuItemSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.menu-item-animate {
    animation: menuItemSlideIn 0.6s ease-out forwards;
}

/* Floating effect for menu items */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-3px);
    }
}

.navbar a:hover {
    animation: float 2s ease-in-out infinite;
}

/* Glowing effect */
@keyframes glow {
    0%, 100% {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }
    50% {
        box-shadow: 0 8px 35px rgba(102, 126, 234, 0.4);
    }
}

.navbar a.active {
    animation: glow 3s ease-in-out infinite;
}

/* Modern Brand Logo */
.navbar-brand {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1002;
}

.brand-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.brand-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    text-decoration: none;
    color: white;
}

.brand-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 18px;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.brand-link:hover .brand-icon {
    transform: rotate(360deg) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-size: 16px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 2px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.brand-tagline {
    font-size: 11px;
    opacity: 0.8;
    font-weight: 400;
    line-height: 1;
}

.navbar ul {
    list-style: none;
    display: flex;
    margin: 0;
    padding: 0;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    margin-left: 200px; /* Space for brand */
}

.navbar li {
    position: relative;
    margin: 0 2px;
}

.navbar a {
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    padding: 18px 24px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    font-weight: 600;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin: 8px 4px;
    font-size: 14px;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.navbar a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2));
    opacity: 0;
    transition: all 0.4s ease;
    border-radius: 12px;
}

.navbar a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateX(-50%);
    border-radius: 2px;
}

.navbar a:hover::before {
    opacity: 1;
}

.navbar a:hover::after {
    width: 80%;
}

.navbar a:hover,
.navbar a.active {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.navbar a.active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.navbar a.active::after {
    width: 90%;
    height: 4px;
    background: linear-gradient(90deg, #ff9a9e, #fecfef, #fecfef);
}

.navbar a i {
    margin-right: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.navbar a:hover i {
    transform: scale(1.1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.navbar a .fa-chevron-down {
    margin-left: 10px;
    margin-right: 0;
    font-size: 12px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0.8;
}

/* Ultra Modern Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown:hover .fa-chevron-down {
    transform: rotate(180deg) scale(1.1);
    opacity: 1;
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + 10px);
    left: 50%;
    transform: translateX(-50%) translateY(-20px) scale(0.95);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    backdrop-filter: blur(20px);
    min-width: 280px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    display: none;
    z-index: 1001;
    border-radius: 20px;
    overflow: hidden;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 8px;
}

.dropdown:hover .dropdown-menu {
    display: block;
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
}

.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    border-bottom: 12px solid rgba(255, 255, 255, 0.95);
    filter: drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.1));
}

.dropdown-menu li {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    margin: 2px 0;
    border-radius: 12px;
    overflow: hidden;
}

.dropdown-menu li:last-child {
    border-bottom: none;
}

.dropdown-menu a {
    padding: 14px 20px;
    font-size: 14px;
    font-weight: 500;
    color: #2c3e50;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 12px;
    position: relative;
    display: flex;
    align-items: center;
    margin: 2px 0;
    background: transparent;
    border: none;
    text-decoration: none;
}

.dropdown-menu a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    border-radius: 12px;
    transition: all 0.3s ease;
    z-index: -1;
}

.dropdown-menu a:hover {
    color: white;
    transform: translateX(8px) scale(1.02);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.dropdown-menu a:hover::before {
    opacity: 1;
}

.dropdown-menu a i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
    font-size: 14px;
    transition: all 0.3s ease;
}

.dropdown-menu a:hover i {
    transform: scale(1.1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Modern Search Bar */
.navbar-search {
    position: absolute;
    right: 280px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1002;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 250px;
    padding: 12px 50px 12px 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
    outline: none;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
    width: 300px;
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
}

.search-btn:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Modern User Dropdown */
.navbar-user {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1002;
}

.user-dropdown {
    position: relative;
}

.user-btn {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 8px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    outline: none;
}

.user-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 14px;
}

.user-name {
    margin-right: 8px;
    font-weight: 500;
}

.user-btn .fa-chevron-down {
    font-size: 10px;
    transition: transform 0.3s ease;
}

.user-dropdown.active .fa-chevron-down {
    transform: rotate(180deg);
}

.user-menu {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    padding: 8px;
    z-index: 1003;
}

.user-dropdown.active .user-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: #2c3e50;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    font-size: 14px;
    margin: 2px 0;
}

.user-menu-item:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateX(5px);
    text-decoration: none;
}

.user-menu-item.logout:hover {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.user-menu-item i {
    margin-right: 10px;
    width: 16px;
    text-align: center;
}

.user-menu-divider {
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
    margin: 8px 0;
}

/* Menu Animation Effects */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown:hover .dropdown-menu {
    animation: slideDown 0.3s ease;
}

/* Active Menu Indicator */
.navbar li.current-menu > a {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

/* Menu Badge for Notifications */
.menu-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Submenu Separator */
.dropdown-menu .menu-separator {
    height: 1px;
    background: rgba(255,255,255,0.1);
    margin: 8px 0;
}

/* Menu Loading State */
.navbar.loading {
    opacity: 0.7;
    pointer-events: none;
}

.navbar.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #fff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Breadcrumb Navigation */
.breadcrumb {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 12px 20px;
    margin: 0 0 20px 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border-left: 4px solid #3498db;
}

.breadcrumb-list {
    list-style: none;
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
    flex-wrap: wrap;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: '/';
    margin: 0 8px;
    color: #6c757d;
    font-weight: normal;
}

.breadcrumb-item a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
    font-weight: 500;
}

.breadcrumb-item a:hover {
    color: #2980b9;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #495057;
    font-weight: 600;
}

.breadcrumb-item i {
    margin-right: 5px;
    font-size: 12px;
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: none;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    transition: all 0.3s ease;
    font-size: 18px;
}

.scroll-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
    background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
}

.scroll-to-top.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Menu Transitions */
.navbar ul {
    transition: all 0.3s ease;
}

.navbar ul.show {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Menu Item Pulse Effect */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
    }
}

.navbar a.pulse {
    animation: pulse 2s infinite;
}

/* Enhanced Footer */
.footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ecf0f1;
    margin-top: 50px;
    padding: 40px 0 20px;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footerGrain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23footerGrain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.footer .container {
    position: relative;
    z-index: 1;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h4 {
    color: #3498db;
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
}

.footer-section h5 {
    color: #ecf0f1;
    margin-bottom: 15px;
    font-size: 1rem;
    font-weight: 600;
    border-bottom: 2px solid #3498db;
    padding-bottom: 5px;
    display: inline-block;
}

.footer-section p {
    margin-bottom: 10px;
    color: #bdc3c7;
    line-height: 1.6;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 8px;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    padding: 5px 0;
}

.footer-links a:hover {
    color: #3498db;
    padding-left: 10px;
}

.footer-links a i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 20px;
    text-align: center;
}

.footer-bottom p {
    margin-bottom: 5px;
    color: #95a5a6;
    font-size: 14px;
}

.footer-bottom p:last-child {
    margin-bottom: 0;
    font-size: 13px;
}

/* Footer responsive */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .footer {
        padding: 30px 0 15px;
    }
}

/* Main Content */
.main-content {
    padding: 20px 0;
    min-height: calc(100vh - 200px);
}

/* Cards */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    font-size: 1.1rem;
}

.card-body {
    padding: 20px;
}

/* Dashboard Stats */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #007bff;
}

.stat-card.success {
    border-left-color: #28a745;
}

.stat-card.warning {
    border-left-color: #ffc107;
}

.stat-card.danger {
    border-left-color: #dc3545;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.stat-card .stat-label {
    color: #666;
    font-size: 0.9rem;
}

/* Forms */
.form-group {
    margin-bottom: 15px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    font-family: 'Hind Siliguri', Arial, sans-serif;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s;
    font-family: 'Hind Siliguri', Arial, sans-serif;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #1e7e34;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

.btn-lg {
    padding: 15px 30px;
    font-size: 16px;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background-color: white;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.table-striped tbody tr:nth-child(odd) {
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: #e9ecef;
}

/* Alerts */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
    font-weight: 500;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Utilities */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.float-left { float: left; }
.float-right { float: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 5px; }
.mb-2 { margin-bottom: 10px; }
.mb-3 { margin-bottom: 15px; }
.mb-4 { margin-bottom: 20px; }
.mb-5 { margin-bottom: 25px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 5px; }
.mt-2 { margin-top: 10px; }
.mt-3 { margin-top: 15px; }
.mt-4 { margin-top: 20px; }
.mt-5 { margin-top: 25px; }

/* Ultra Modern Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    font-size: 18px;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 12px;
    margin: 8px;
    position: relative;
    overflow: hidden;
}

.mobile-menu-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.mobile-menu-toggle:hover::before {
    left: 100%;
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.mobile-menu-toggle i {
    transition: transform 0.3s ease;
}

.mobile-menu-toggle:hover i {
    transform: rotate(90deg);
}

/* Responsive */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
    }

    .navbar-brand {
        position: static;
        transform: none;
        margin: 10px 20px;
    }

    .navbar-search,
    .navbar-user {
        display: none; /* Hide on mobile for cleaner look */
    }

    .navbar ul {
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.98) 0%, rgba(118, 75, 162, 0.98) 50%, rgba(240, 147, 251, 0.98) 100%);
        backdrop-filter: blur(20px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        display: none;
        z-index: 999;
        margin-left: 0;
        padding: 20px 0;
        border-radius: 0 0 20px 20px;
    }

    .navbar ul.show {
        display: flex;
        animation: slideDownMobile 0.4s ease-out;
    }

    @keyframes slideDownMobile {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .navbar li {
        width: 100%;
        margin: 4px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .navbar li:last-child {
        border-bottom: none;
    }

    .navbar a {
        padding: 16px 20px;
        border-radius: 12px;
        margin: 4px 0;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .navbar a:hover {
        transform: translateX(10px);
        background: rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .dropdown-menu {
        position: static;
        display: none;
        box-shadow: none;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        opacity: 1;
        transform: none;
        min-width: auto;
        margin: 8px 0;
        padding: 8px;
    }

    .dropdown-menu::before {
        display: none;
    }

    .dropdown.active .dropdown-menu {
        display: block;
        animation: slideDownSubmenu 0.3s ease-out;
    }

    @keyframes slideDownSubmenu {
        from {
            opacity: 0;
            max-height: 0;
        }
        to {
            opacity: 1;
            max-height: 300px;
        }
    }

    .dropdown-menu a {
        padding: 12px 20px;
        font-size: 13px;
        color: white;
        background: rgba(255, 255, 255, 0.05);
        margin: 2px 0;
        border-radius: 8px;
    }

    .dropdown-menu a:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateX(8px);
        padding-left: 24px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .table {
        font-size: 12px;
    }

    .table th,
    .table td {
        padding: 8px;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.4rem;
    }

    .header .user-info {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    .navbar a {
        padding: 12px 15px;
        font-size: 13px;
    }

    .dropdown-menu a {
        padding: 10px 30px;
        font-size: 12px;
    }

    .scroll-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
        font-size: 16px;
    }

    .breadcrumb {
        padding: 10px 15px;
        font-size: 13px;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        margin: 0 6px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .breadcrumb {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: #ecf0f1;
        border-left-color: #3498db;
    }

    .breadcrumb-item a {
        color: #3498db;
    }

    .breadcrumb-item.active {
        color: #ecf0f1;
    }
}

/* Print styles */
@media print {
    .navbar,
    .scroll-to-top,
    .mobile-menu-toggle,
    .footer {
        display: none !important;
    }

    .main-content {
        padding: 0;
    }

    .breadcrumb {
        background: none;
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Enhanced Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(52, 152, 219, 0.3);
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Loading overlay */
#loading-screen {
    backdrop-filter: blur(5px);
}

#loading-screen > div {
    background: rgba(255, 255, 255, 0.1);
    padding: 30px;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Menu hover effects */
.navbar a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: #3498db;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar a:hover::after,
.navbar a.active::after {
    width: 80%;
}

/* Dropdown menu improvements */
.dropdown-menu {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-menu li:hover {
    background: rgba(52, 152, 219, 0.1);
}

/* Menu accessibility improvements */
.navbar a:focus,
.dropdown-menu a:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

/* Menu loading state */
.navbar.loading::after {
    border-color: rgba(255, 255, 255, 0.3);
    border-top-color: #fff;
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Menu hover effect enhancement */
.menu-hover-effect {
    position: relative;
    overflow: hidden;
}

.menu-hover-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Advanced menu states */
.navbar a.loading {
    position: relative;
    pointer-events: none;
}

.navbar a.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translateY(-50%);
}

/* Responsive navbar improvements */
@media (max-width: 1200px) {
    .navbar ul {
        margin-left: 180px;
    }

    .navbar-search {
        right: 250px;
    }

    .search-input {
        width: 200px;
    }

    .search-input:focus {
        width: 240px;
    }
}

@media (max-width: 992px) {
    .navbar-brand {
        position: static;
        transform: none;
        margin-bottom: 10px;
    }

    .navbar ul {
        margin-left: 0;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .navbar-search,
    .navbar-user {
        position: static;
        transform: none;
        margin: 10px 0;
    }

    .search-input {
        width: 100%;
        max-width: 300px;
    }
}

/* Dark theme support for navbar */
@media (prefers-color-scheme: dark) {
    .navbar {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    }

    .navbar.scrolled {
        background: linear-gradient(135deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 50%, rgba(15, 52, 96, 0.95) 100%);
    }
}

/* Print styles for navbar */
@media print {
    .navbar {
        display: none !important;
    }
}

/* Enhanced Notification System */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 300px;
    max-width: 400px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    border-left: 4px solid #3498db;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-content {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-icon {
    font-size: 18px;
    flex-shrink: 0;
}

.notification-message {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
}

.notification-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.notification-close:hover {
    background: #f5f5f5;
    color: #666;
}

/* Notification types */
.notification-success {
    border-left-color: #28a745;
}

.notification-success .notification-icon {
    color: #28a745;
}

.notification-error {
    border-left-color: #dc3545;
}

.notification-error .notification-icon {
    color: #dc3545;
}

.notification-warning {
    border-left-color: #ffc107;
}

.notification-warning .notification-icon {
    color: #ffc107;
}

.notification-info {
    border-left-color: #17a2b8;
}

.notification-info .notification-icon {
    color: #17a2b8;
}

/* Multiple notifications */
.notification:nth-child(n+2) {
    top: calc(20px + (80px * var(--notification-index, 1)));
}

/* Menu search styles */
.menu-search {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);
}

.menu-search::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.menu-search:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

/* Menu performance indicators */
.menu-loading {
    position: relative;
}

.menu-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translateY(-50%);
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
