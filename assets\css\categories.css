/* Ultra Modern Categories Page CSS */

/* Categories Container */
.categories-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
    width: 100%;
    overflow-x: hidden;
}

/* Modern Page Header */
.page-header-modern {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.page-header-modern::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: headerGlow 6s ease-in-out infinite;
}

@keyframes headerGlow {
    0%, 100% { transform: rotate(0deg); opacity: 0.5; }
    50% { transform: rotate(180deg); opacity: 1; }
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.header-title-section {
    flex: 1;
}

.page-title {
    display: flex;
    align-items: center;
    gap: 20px;
    margin: 0;
    color: white;
}

.title-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.title-text {
    display: flex;
    flex-direction: column;
}

.main-title {
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.sub-title {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-top: 5px;
    font-weight: 400;
}

.header-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    font-size: 14px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.action-btn.secondary {
    background: rgba(255, 255, 255, 0.15);
}

.action-btn.info {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
}

.action-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

/* Statistics Row */
.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card-modern.success::before {
    background: linear-gradient(90deg, #4ecdc4, #44a08d);
}

.stat-card-modern.info::before {
    background: linear-gradient(90deg, #4facfe, #00f2fe);
}

.stat-card-modern.warning::before {
    background: linear-gradient(90deg, #f093fb, #f5576c);
}

.stat-card-modern:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.stat-card-modern .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-card-modern.success .stat-icon {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.stat-card-modern.info .stat-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-card-modern.warning .stat-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-content {
    margin-bottom: 15px;
}

.stat-number {
    font-size: 2.2rem;
    font-weight: 800;
    color: #2c3e50;
    line-height: 1;
    margin-bottom: 5px;
}

.stat-label {
    color: #7f8c8d;
    font-size: 0.95rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    color: #27ae60;
    font-weight: 500;
}

/* Search and Filter Section */
.search-filter-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-container-modern {
    flex: 1;
    min-width: 300px;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 15px;
    color: #7f8c8d;
    font-size: 16px;
    z-index: 1;
}

.search-input-modern {
    width: 100%;
    padding: 15px 50px 15px 45px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    outline: none;
}

.search-input-modern:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.search-clear-btn {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.search-clear-btn:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #e74c3c;
}

.filter-container-modern {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-select-modern {
    padding: 12px 15px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    outline: none;
    min-width: 150px;
}

.filter-select-modern:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.filter-btn-modern {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.filter-btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.category-card-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.4s ease;
    position: relative;
    cursor: pointer;
}

.category-card-modern:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 70px rgba(0, 0, 0, 0.15);
}

.category-image {
    height: 180px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-placeholder {
    font-size: 3rem;
    color: rgba(255, 255, 255, 0.7);
}

.category-status {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.category-status.active {
    background: rgba(39, 174, 96, 0.9);
    color: white;
}

.category-status.inactive {
    background: rgba(231, 76, 60, 0.9);
    color: white;
}

.category-content {
    padding: 25px;
}

.category-name {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
    line-height: 1.2;
}

.category-description {
    color: #7f8c8d;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 20px;
}

.category-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.stat-item i {
    color: #667eea;
}

.category-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    padding: 0 25px 25px;
}

.action-btn-small {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    color: white;
}

.action-btn-small.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.action-btn-small.success {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.action-btn-small.warning {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.action-btn-small.danger {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.action-btn-small:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Pagination */
.pagination-modern {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 40px;
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #2c3e50;
}

.pagination-btn:hover:not(:disabled) {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-numbers {
    display: flex;
    gap: 5px;
}

.pagination-number {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.95);
    color: #2c3e50;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.pagination-number.active {
    background: #667eea;
    color: white;
}

.pagination-number:hover {
    background: #667eea;
    color: white;
    transform: scale(1.1);
}

.pagination-dots {
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #7f8c8d;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: modalFadeIn 0.3s ease;
}

.modal-overlay.show {
    display: flex;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal-container-modern {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.4s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

.modal-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.modal-title i {
    color: #667eea;
}

.modal-close {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 10px;
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.modal-close:hover {
    background: #e74c3c;
    color: white;
    transform: scale(1.1);
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding: 25px 30px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(0, 0, 0, 0.02);
}

/* Form Styles */
.form-modern {
    width: 100%;
}

.form-row {
    margin-bottom: 25px;
}

.form-group {
    width: 100%;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-label i {
    color: #667eea;
    font-size: 14px;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    outline: none;
    font-family: inherit;
}

.form-input:focus,
.form-textarea:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* File Upload */
.file-upload-area {
    border: 2px dashed rgba(102, 126, 234, 0.3);
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    background: rgba(102, 126, 234, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.file-upload-area:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.file-upload-content i {
    font-size: 2rem;
    color: #667eea;
}

.file-upload-content span {
    font-weight: 600;
    color: #2c3e50;
}

.file-upload-content small {
    color: #7f8c8d;
    font-size: 12px;
}

/* Toggle Switch */
.toggle-switch {
    display: flex;
    align-items: center;
    gap: 15px;
}

.toggle-input {
    display: none;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
}

.toggle-slider {
    width: 50px;
    height: 26px;
    background: #ccc;
    border-radius: 13px;
    position: relative;
    transition: all 0.3s ease;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 22px;
    height: 22px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-input:checked + .toggle-label .toggle-slider {
    background: #667eea;
}

.toggle-input:checked + .toggle-label .toggle-slider::before {
    transform: translateX(24px);
}

.toggle-text {
    font-weight: 600;
    color: #2c3e50;
}

/* Button Styles */
.btn-modern {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    text-decoration: none;
}

.btn-modern.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-modern.secondary {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .categories-container {
        padding: 10px 0;
    }

    .page-header-modern {
        padding: 20px;
    }

    .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .main-title {
        font-size: 2rem;
    }

    .header-actions {
        justify-content: center;
    }

    .stats-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .search-filter-section {
        flex-direction: column;
        gap: 15px;
    }

    .filter-container-modern {
        width: 100%;
        justify-content: center;
    }

    .categories-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .modal-container-modern {
        width: 95%;
        margin: 20px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .page-title {
        flex-direction: column;
        gap: 15px;
    }

    .title-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .main-title {
        font-size: 1.8rem;
    }

    .action-btn {
        padding: 10px 16px;
        font-size: 13px;
    }

    .category-actions {
        flex-wrap: wrap;
        gap: 5px;
    }

    .action-btn-small {
        width: 35px;
        height: 35px;
        font-size: 12px;
    }
}
