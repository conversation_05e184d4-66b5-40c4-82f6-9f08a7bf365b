<?php
// Additional Helper Functions for Dokan Management System
// Note: toBengaliNumber() is already defined in functions.php

/**
 * Format currency in Bengali Taka
 */
function formatCurrency($amount, $symbol = '৳') {
    $formatted = number_format($amount, 2);
    return $symbol . ' ' . toBengaliNumber($formatted);
}

/**
 * Get Bengali month name
 */
function getBengaliMonth($month_number) {
    $bengali_months = [
        1 => 'জানুয়ারি',
        2 => 'ফেব্রুয়ারি',
        3 => 'মার্চ',
        4 => 'এপ্রিল',
        5 => 'মে',
        6 => 'জুন',
        7 => 'জুলাই',
        8 => 'আগস্ট',
        9 => 'সেপ্টেম্বর',
        10 => 'অক্টোবর',
        11 => 'নভেম্বর',
        12 => 'ডিসেম্বর'
    ];

    return $bengali_months[$month_number] ?? '';
}

/**
 * Get Bengali day name
 */
function getBengaliDay($day_number) {
    $bengali_days = [
        0 => 'রবিবার',
        1 => 'সোমবার',
        2 => 'মঙ্গলবার',
        3 => 'বুধবার',
        4 => 'বৃহস্পতিবার',
        5 => 'শুক্রবার',
        6 => 'শনিবার'
    ];

    return $bengali_days[$day_number] ?? '';
}

/**
 * Format full Bengali date with day name
 */
function formatFullBengaliDate($date = null) {
    if ($date === null) {
        $date = new DateTime();
    } elseif (is_string($date)) {
        $date = new DateTime($date);
    }

    $day = $date->format('w');
    $day_name = getBengaliDay($day);
    $day_number = toBengaliNumber($date->format('d'));
    $month = getBengaliMonth($date->format('n'));
    $year = toBengaliNumber($date->format('Y'));

    return "{$day_name}, {$day_number} {$month} {$year}";
}

/**
 * Simple date format for categories (d/m/Y format)
 */
function formatDate($date, $format = 'd/m/Y') {
    if (is_string($date)) {
        $date = new DateTime($date);
    }

    $formatted = $date->format($format);
    return toBengaliNumber($formatted);
}

/**
 * Time ago in Bengali
 */
function timeAgoBengali($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return toBengaliNumber($time) . ' সেকেন্ড আগে';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return toBengaliNumber($minutes) . ' মিনিট আগে';
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return toBengaliNumber($hours) . ' ঘন্টা আগে';
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return toBengaliNumber($days) . ' দিন আগে';
    } elseif ($time < 31536000) {
        $months = floor($time / 2592000);
        return toBengaliNumber($months) . ' মাস আগে';
    } else {
        $years = floor($time / 31536000);
        return toBengaliNumber($years) . ' বছর আগে';
    }
}

/**
 * Generate random Bengali OTP
 */
function generateBengaliOTP($length = 6) {
    $otp = '';
    for ($i = 0; $i < $length; $i++) {
        $otp .= rand(0, 9);
    }
    return toBengaliNumber($otp);
}

/**
 * Sanitize input for Bengali text
 */
function sanitizeBengaliInput($input) {
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return $input;
}

/**
 * Generate slug from Bengali text
 */
function generateSlug($text) {
    // Convert Bengali to English transliteration (basic)
    $bengali_to_english = [
        'ক' => 'k', 'খ' => 'kh', 'গ' => 'g', 'ঘ' => 'gh', 'ঙ' => 'ng',
        'চ' => 'ch', 'ছ' => 'chh', 'জ' => 'j', 'ঝ' => 'jh', 'ঞ' => 'ny',
        'ট' => 't', 'ঠ' => 'th', 'ড' => 'd', 'ঢ' => 'dh', 'ণ' => 'n',
        'ত' => 't', 'থ' => 'th', 'দ' => 'd', 'ধ' => 'dh', 'ন' => 'n',
        'প' => 'p', 'ফ' => 'ph', 'ব' => 'b', 'ভ' => 'bh', 'ম' => 'm',
        'য' => 'y', 'র' => 'r', 'ল' => 'l', 'শ' => 'sh', 'ষ' => 'sh',
        'স' => 's', 'হ' => 'h', 'া' => 'a', 'ি' => 'i', 'ী' => 'i',
        'ু' => 'u', 'ূ' => 'u', 'ে' => 'e', 'ৈ' => 'oi', 'ো' => 'o',
        'ৌ' => 'ou', 'ং' => 'ng', 'ঃ' => 'h', '্' => '', ' ' => '-'
    ];
    
    $slug = strtr($text, $bengali_to_english);
    $slug = strtolower($slug);
    $slug = preg_replace('/[^a-z0-9-]/', '', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    $slug = trim($slug, '-');
    
    return $slug;
}

/**
 * Format file size in Bengali
 */
function formatFileSize($bytes) {
    $units = ['বাইট', 'কেবি', 'এমবি', 'জিবি', 'টিবি'];
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return toBengaliNumber(round($bytes, 2)) . ' ' . $units[$i];
}

/**
 * Get status badge HTML
 */
function getStatusBadge($status, $active_text = 'সক্রিয়', $inactive_text = 'নিষ্ক্রিয়') {
    if ($status === 'active' || $status === 1 || $status === true) {
        return '<span class="badge badge-success"><i class="fas fa-check-circle"></i> ' . $active_text . '</span>';
    } else {
        return '<span class="badge badge-danger"><i class="fas fa-times-circle"></i> ' . $inactive_text . '</span>';
    }
}

/**
 * Generate breadcrumb HTML
 */
function generateBreadcrumb($breadcrumbs) {
    $html = '<nav class="breadcrumb-modern"><ol class="breadcrumb-list">';
    
    foreach ($breadcrumbs as $index => $crumb) {
        $isLast = ($index === count($breadcrumbs) - 1);
        
        $html .= '<li class="breadcrumb-item' . ($isLast ? ' active' : '') . '">';
        
        if (isset($crumb['icon'])) {
            $html .= '<i class="' . $crumb['icon'] . '"></i> ';
        }
        
        if (!$isLast && isset($crumb['url'])) {
            $html .= '<a href="' . $crumb['url'] . '">' . $crumb['title'] . '</a>';
        } else {
            $html .= $crumb['title'];
        }
        
        $html .= '</li>';
        
        if (!$isLast) {
            $html .= '<li class="breadcrumb-separator"><i class="fas fa-chevron-right"></i></li>';
        }
    }
    
    $html .= '</ol></nav>';
    return $html;
}

/**
 * Generate pagination HTML
 */
function generatePagination($current_page, $total_pages, $base_url) {
    if ($total_pages <= 1) return '';
    
    $html = '<div class="pagination-modern">';
    
    // Previous button
    if ($current_page > 1) {
        $prev_page = $current_page - 1;
        $html .= '<a href="' . $base_url . '?page=' . $prev_page . '" class="pagination-btn prev">';
        $html .= '<i class="fas fa-chevron-left"></i> পূর্ববর্তী</a>';
    } else {
        $html .= '<button class="pagination-btn prev" disabled>';
        $html .= '<i class="fas fa-chevron-left"></i> পূর্ববর্তী</button>';
    }
    
    // Page numbers
    $html .= '<div class="pagination-numbers">';
    
    $start = max(1, $current_page - 2);
    $end = min($total_pages, $current_page + 2);
    
    if ($start > 1) {
        $html .= '<a href="' . $base_url . '?page=1" class="pagination-number">' . toBengaliNumber(1) . '</a>';
        if ($start > 2) {
            $html .= '<span class="pagination-dots">...</span>';
        }
    }
    
    for ($i = $start; $i <= $end; $i++) {
        $active = ($i == $current_page) ? ' active' : '';
        $html .= '<a href="' . $base_url . '?page=' . $i . '" class="pagination-number' . $active . '">';
        $html .= toBengaliNumber($i) . '</a>';
    }
    
    if ($end < $total_pages) {
        if ($end < $total_pages - 1) {
            $html .= '<span class="pagination-dots">...</span>';
        }
        $html .= '<a href="' . $base_url . '?page=' . $total_pages . '" class="pagination-number">';
        $html .= toBengaliNumber($total_pages) . '</a>';
    }
    
    $html .= '</div>';
    
    // Next button
    if ($current_page < $total_pages) {
        $next_page = $current_page + 1;
        $html .= '<a href="' . $base_url . '?page=' . $next_page . '" class="pagination-btn next">';
        $html .= 'পরবর্তী <i class="fas fa-chevron-right"></i></a>';
    } else {
        $html .= '<button class="pagination-btn next" disabled>';
        $html .= 'পরবর্তী <i class="fas fa-chevron-right"></i></button>';
    }
    
    $html .= '</div>';
    return $html;
}

/**
 * Log activity
 */
function logActivity($user_id, $action, $details = '') {
    // For now, just log to file
    $log_file = __DIR__ . '/../logs/activity.log';
    $log_line = date('Y-m-d H:i:s') . " - User: {$user_id}, Action: {$action}, Details: {$details}\n";

    // Create logs directory if it doesn't exist
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }

    file_put_contents($log_file, $log_line, FILE_APPEND | LOCK_EX);
}

/**
 * Send notification
 */
function sendNotification($user_id, $title, $message, $type = 'info') {
    // This would typically save to database and/or send real notification
    $notification = [
        'user_id' => $user_id,
        'title' => $title,
        'message' => $message,
        'type' => $type,
        'created_at' => date('Y-m-d H:i:s'),
        'read' => false
    ];
    
    // For now, just return the notification array
    return $notification;
}

/**
 * Validate Bengali phone number
 */
function validateBengaliPhone($phone) {
    // Remove Bengali numbers and convert to English
    $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    $bengali_digits = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
    
    $phone = str_replace($bengali_digits, $english_digits, $phone);
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Bangladesh phone number validation
    if (preg_match('/^(?:\+88|88)?01[3-9]\d{8}$/', $phone)) {
        return $phone;
    }
    
    return false;
}

/**
 * Generate QR code URL
 */
function generateQRCode($data, $size = 200) {
    $encoded_data = urlencode($data);
    return "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data={$encoded_data}";
}
?>
