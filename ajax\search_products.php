<?php
require_once '../config/config.php';
requireAuth();

$db = new Database();
$conn = $db->getConnection();

$term = isset($_GET['term']) ? sanitize($_GET['term']) : '';
$results = [];

if (!empty($term)) {
    $query = "SELECT id, name, selling_price, stock_quantity, unit 
              FROM products 
              WHERE (name LIKE :term OR barcode LIKE :term) 
              AND status = 'active' 
              AND stock_quantity > 0
              ORDER BY name 
              LIMIT 10";
    
    $stmt = $conn->prepare($query);
    $stmt->bindValue(':term', "%$term%");
    $stmt->execute();
    
    $products = $stmt->fetchAll();
    
    foreach ($products as $product) {
        $results[] = [
            'id' => $product['id'],
            'label' => $product['name'] . ' (স্টক: ' . $product['stock_quantity'] . ' ' . $product['unit'] . ')',
            'value' => $product['name'],
            'name' => $product['name'],
            'selling_price' => $product['selling_price'],
            'stock_quantity' => $product['stock_quantity'],
            'unit' => $product['unit']
        ];
    }
}

header('Content-Type: application/json');
echo json_encode($results);
?>
