<?php
// Function Conflict Checker and Fixer
// Run this script to check and fix function conflicts

echo "🔧 Function Conflict Checker Started...\n\n";

// List of files to check
$files_to_check = [
    'includes/functions.php',
    'includes/helpers.php'
];

// Functions that might conflict
$functions_to_check = [
    'toBengaliNumber',
    'formatDate',
    'formatBengaliDate',
    'formatCurrency',
    'getBengaliMonth',
    'getBengaliDay'
];

echo "📁 Checking files for function conflicts:\n";

$all_functions = [];
$conflicts = [];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "   ✅ {$file}\n";
        
        $content = file_get_contents($file);
        
        // Find function declarations
        preg_match_all('/function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/', $content, $matches);
        
        if (!empty($matches[1])) {
            foreach ($matches[1] as $function_name) {
                if (in_array($function_name, $functions_to_check)) {
                    if (isset($all_functions[$function_name])) {
                        $conflicts[] = [
                            'function' => $function_name,
                            'files' => [$all_functions[$function_name], $file]
                        ];
                    } else {
                        $all_functions[$function_name] = $file;
                    }
                }
            }
        }
    } else {
        echo "   ❌ {$file} (not found)\n";
    }
}

echo "\n📊 Function Analysis:\n";

if (empty($conflicts)) {
    echo "   ✅ No function conflicts found!\n";
} else {
    echo "   ⚠️  Function conflicts detected:\n";
    foreach ($conflicts as $conflict) {
        echo "      🔴 {$conflict['function']}() found in:\n";
        foreach ($conflict['files'] as $file) {
            echo "         - {$file}\n";
        }
    }
}

echo "\n🔍 Checking current function status:\n";

foreach ($functions_to_check as $function) {
    if (function_exists($function)) {
        echo "   ✅ {$function}() - Available\n";
    } else {
        echo "   ❌ {$function}() - Not available\n";
    }
}

// Test toBengaliNumber function
echo "\n🧪 Testing toBengaliNumber function:\n";

if (function_exists('toBengaliNumber')) {
    $test_numbers = [123, 456, 789, 2024];
    foreach ($test_numbers as $num) {
        $bengali = toBengaliNumber($num);
        echo "   {$num} → {$bengali}\n";
    }
} else {
    echo "   ❌ toBengaliNumber() function not available\n";
}

// Create a safe functions file
echo "\n🛠️  Creating safe functions file...\n";

$safe_functions_content = '<?php
// Safe Functions - No Conflicts
// This file contains only unique functions

// Check if toBengaliNumber exists, if not create it
if (!function_exists("toBengaliNumber")) {
    function toBengaliNumber($number) {
        $bengali_digits = ["০", "১", "২", "৩", "৪", "৫", "৬", "৭", "৮", "৯"];
        $english_digits = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        return str_replace($english_digits, $bengali_digits, (string)$number);
    }
}

// Check if formatDate exists, if not create it
if (!function_exists("formatDate")) {
    function formatDate($date, $format = "d/m/Y") {
        if (is_string($date)) {
            $date = new DateTime($date);
        }
        $formatted = $date->format($format);
        return toBengaliNumber($formatted);
    }
}

// Check if formatCurrency exists, if not create it
if (!function_exists("formatCurrency")) {
    function formatCurrency($amount, $symbol = "৳") {
        $formatted = number_format($amount, 2);
        return $symbol . " " . toBengaliNumber($formatted);
    }
}

// Check if getBengaliMonth exists, if not create it
if (!function_exists("getBengaliMonth")) {
    function getBengaliMonth($month_number) {
        $bengali_months = [
            1 => "জানুয়ারি", 2 => "ফেব্রুয়ারি", 3 => "মার্চ", 4 => "এপ্রিল",
            5 => "মে", 6 => "জুন", 7 => "জুলাই", 8 => "আগস্ট",
            9 => "সেপ্টেম্বর", 10 => "অক্টোবর", 11 => "নভেম্বর", 12 => "ডিসেম্বর"
        ];
        return $bengali_months[$month_number] ?? "";
    }
}

// Check if getBengaliDay exists, if not create it
if (!function_exists("getBengaliDay")) {
    function getBengaliDay($day_number) {
        $bengali_days = [
            0 => "রবিবার", 1 => "সোমবার", 2 => "মঙ্গলবার", 3 => "বুধবার",
            4 => "বৃহস্পতিবার", 5 => "শুক্রবার", 6 => "শনিবার"
        ];
        return $bengali_days[$day_number] ?? "";
    }
}
?>';

file_put_contents('includes/safe_functions.php', $safe_functions_content);
echo "   ✅ Created includes/safe_functions.php\n";

// Update categories index.php to use safe functions
echo "\n📝 Updating categories/index.php...\n";

$categories_file = 'categories/index.php';
if (file_exists($categories_file)) {
    $content = file_get_contents($categories_file);
    
    // Replace the includes
    $old_includes = 'require_once \'../includes/functions.php\';
require_once \'../includes/helpers.php\';';
    
    $new_includes = 'require_once \'../includes/safe_functions.php\';';
    
    $updated_content = str_replace($old_includes, $new_includes, $content);
    
    if ($updated_content !== $content) {
        file_put_contents($categories_file, $updated_content);
        echo "   ✅ Updated categories/index.php includes\n";
    } else {
        echo "   ℹ️  No changes needed in categories/index.php\n";
    }
} else {
    echo "   ❌ categories/index.php not found\n";
}

echo "\n🎉 Function conflict fix completed!\n";
echo "📋 Summary:\n";
echo "   - Created safe_functions.php with conflict-free functions\n";
echo "   - Updated categories/index.php to use safe functions\n";
echo "   - All functions should now work without conflicts\n";

echo "\n🔗 Next steps:\n";
echo "   1. Test the categories page: http://localhost/dokan/categories/index.php\n";
echo "   2. If everything works, you can remove this fix_functions.php file\n";
echo "   3. Consider consolidating all functions into one file in the future\n";

echo "\n✅ Done!\n";
?>
