<?php
// Menu test page
require_once 'config/config.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$page_title = 'মেনু টেস্ট';

include 'includes/header.php';
?>

<div class="container-fluid" style="padding: 20px;">
    <div class="card">
        <div class="card-header">
            <h3><i class="fas fa-bug"></i> মেনু টেস্ট পেজ</h3>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h4>মেনু টেস্ট নির্দেশনা:</h4>
                <ul>
                    <li><strong>ডেস্কটপে:</strong> মেনু আইটেমের উপর মাউস হোভার করুন</li>
                    <li><strong>মোবাইলে:</strong> হ্যামবার্গার মেনু ক্লিক করুন, তারপর ড্রপডাউন মেনুতে ক্লিক করুন</li>
                    <li><strong>সাবমেনু:</strong> মূল মেনুর ঠিক নিচে আসা উচিত, পাশে নয়</li>
                    <li><strong>লুপিং:</strong> সাবমেনু বার বার খোলা-বন্ধ হওয়া উচিত নয়</li>
                </ul>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>✅ সমাধান করা সমস্যা:</h4>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <i class="fas fa-check text-success"></i>
                            সাবমেনু পজিশনিং ঠিক করা হয়েছে
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check text-success"></i>
                            লুপিং সমস্যা সমাধান করা হয়েছে
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check text-success"></i>
                            মোবাইল রেসপন্সিভ উন্নত করা হয়েছে
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check text-success"></i>
                            হোভার ইফেক্ট স্থিতিশীল করা হয়েছে
                        </li>
                    </ul>
                </div>
                
                <div class="col-md-6">
                    <h4>🔧 প্রযুক্তিগত পরিবর্তন:</h4>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <strong>CSS:</strong> ড্রপডাউন পজিশনিং আপডেট
                        </li>
                        <li class="list-group-item">
                            <strong>JavaScript:</strong> ইভেন্ট হ্যান্ডলিং উন্নত
                        </li>
                        <li class="list-group-item">
                            <strong>Flexbox:</strong> নেভবার লেআউট পুনর্গঠন
                        </li>
                        <li class="list-group-item">
                            <strong>Z-index:</strong> স্তর ব্যবস্থাপনা উন্নত
                        </li>
                    </ul>
                </div>
            </div>
            
            <hr>
            
            <div class="text-center">
                <h4>🧪 টেস্ট করুন:</h4>
                <p>উপরের মেনুবারে বিভিন্ন ড্রপডাউন মেনু টেস্ট করুন</p>
                
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-primary" onclick="testDesktopMenu()">
                        <i class="fas fa-desktop"></i> ডেস্কটপ মেনু টেস্ট
                    </button>
                    <button type="button" class="btn btn-success" onclick="testMobileMenu()">
                        <i class="fas fa-mobile-alt"></i> মোবাইল মেনু টেস্ট
                    </button>
                    <button type="button" class="btn btn-info" onclick="checkMenuPosition()">
                        <i class="fas fa-crosshairs"></i> পজিশন চেক
                    </button>
                </div>
            </div>
            
            <div id="test-results" class="mt-4" style="display: none;">
                <div class="alert alert-success">
                    <h5>টেস্ট ফলাফল:</h5>
                    <div id="test-output"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testDesktopMenu() {
    const results = [];
    
    // Check dropdown positioning
    $('.dropdown').each(function(index) {
        const $dropdown = $(this);
        const $menu = $dropdown.find('.dropdown-menu');
        
        // Simulate hover
        $dropdown.trigger('mouseenter');
        
        setTimeout(() => {
            const isVisible = $menu.is(':visible');
            const position = $menu.css('position');
            const top = $menu.css('top');
            const left = $menu.css('left');
            
            results.push(`ড্রপডাউন ${index + 1}: ${isVisible ? '✅ দৃশ্যমান' : '❌ লুকানো'}, Position: ${position}, Top: ${top}, Left: ${left}`);
            
            if (index === $('.dropdown').length - 1) {
                showTestResults(results);
            }
            
            $dropdown.trigger('mouseleave');
        }, 100);
    });
}

function testMobileMenu() {
    const results = [];
    
    // Simulate mobile view
    if ($(window).width() > 768) {
        results.push('⚠️ মোবাইল টেস্টের জন্য ব্রাউজার রিসাইজ করুন বা ডেভেলপার টুলস ব্যবহার করুন');
    } else {
        results.push('✅ মোবাইল ভিউ সনাক্ত করা হয়েছে');
        
        // Test mobile menu toggle
        const $toggle = $('#mobile-menu-toggle');
        const $menu = $('#navbar-menu');
        
        $toggle.click();
        
        setTimeout(() => {
            const isVisible = $menu.hasClass('show');
            results.push(`মোবাইল মেনু: ${isVisible ? '✅ খোলা' : '❌ বন্ধ'}`);
            showTestResults(results);
        }, 100);
    }
    
    if (results.length === 1 && results[0].includes('রিসাইজ')) {
        showTestResults(results);
    }
}

function checkMenuPosition() {
    const results = [];
    
    $('.dropdown-menu').each(function(index) {
        const $menu = $(this);
        const $parent = $menu.parent();
        
        const parentOffset = $parent.offset();
        const menuOffset = $menu.offset();
        
        const isCorrectPosition = menuOffset && parentOffset && 
                                 Math.abs(menuOffset.left - parentOffset.left) < 50;
        
        results.push(`সাবমেনু ${index + 1}: ${isCorrectPosition ? '✅ সঠিক পজিশন' : '❌ ভুল পজিশন'}`);
    });
    
    showTestResults(results);
}

function showTestResults(results) {
    const $output = $('#test-output');
    const $container = $('#test-results');
    
    $output.html('<ul><li>' + results.join('</li><li>') + '</li></ul>');
    $container.show();
    
    // Auto hide after 10 seconds
    setTimeout(() => {
        $container.fadeOut();
    }, 10000);
}

// Auto test on page load
$(document).ready(function() {
    setTimeout(() => {
        showNotification('মেনু টেস্ট পেজ লোড হয়েছে। উপরের মেনু টেস্ট করুন।', 'info', 5000);
    }, 1000);
});
</script>

<style>
.list-group-item {
    border: 1px solid rgba(0,0,0,0.125);
    padding: 12px 15px;
    margin-bottom: 5px;
    border-radius: 8px;
    background: rgba(255,255,255,0.8);
}

.btn-group .btn {
    margin: 5px;
}

#test-results {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<?php include 'includes/footer.php'; ?>
