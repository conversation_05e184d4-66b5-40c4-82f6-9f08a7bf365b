<?php
require_once 'config/config.php';
requireAuth();

$db = new Database();
$conn = $db->getConnection();

// Get dashboard statistics
$stats = [];

// Total products
$query = "SELECT COUNT(*) as count FROM products WHERE status = 'active'";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['total_products'] = $stmt->fetch()['count'];

// Total customers
$query = "SELECT COUNT(*) as count FROM customers WHERE status = 'active'";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['total_customers'] = $stmt->fetch()['count'];

// Total suppliers
$query = "SELECT COUNT(*) as count FROM suppliers WHERE status = 'active'";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['total_suppliers'] = $stmt->fetch()['count'];

// Today's sales
$query = "SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM sales WHERE DATE(created_at) = CURDATE()";
$stmt = $conn->prepare($query);
$stmt->execute();
$today_sales = $stmt->fetch();
$stats['today_sales_count'] = $today_sales['count'];
$stats['today_sales_amount'] = $today_sales['total'];

// This month's sales
$query = "SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM sales WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())";
$stmt = $conn->prepare($query);
$stmt->execute();
$month_sales = $stmt->fetch();
$stats['month_sales_count'] = $month_sales['count'];
$stats['month_sales_amount'] = $month_sales['total'];

// Low stock products
$low_stock_products = getLowStockProducts();
$stats['low_stock_count'] = count($low_stock_products);

// Expired products
$expired_products = getExpiredProducts();
$stats['expired_count'] = count($expired_products);

// Products expiring soon
$expiring_products = getExpiringSoonProducts();
$stats['expiring_soon_count'] = count($expiring_products);

// Recent sales
$query = "SELECT s.*, c.name as customer_name FROM sales s 
          LEFT JOIN customers c ON s.customer_id = c.id 
          ORDER BY s.created_at DESC LIMIT 10";
$stmt = $conn->prepare($query);
$stmt->execute();
$recent_sales = $stmt->fetchAll();

// Customer dues
$query = "SELECT SUM(balance) as total_due FROM customers WHERE balance > 0";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['customer_dues'] = $stmt->fetch()['total_due'] ?? 0;

// Supplier dues
$query = "SELECT SUM(balance) as total_due FROM suppliers WHERE balance > 0";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['supplier_dues'] = $stmt->fetch()['total_due'] ?? 0;

include 'includes/header.php';
?>

<div class="dashboard-container">
    <div class="container-fluid">
        <!-- Modern Dashboard Header -->
        <div class="dashboard-header">
            <div class="dashboard-date">
                <i class="fas fa-calendar-alt"></i>
                <?php echo formatDate(date('Y-m-d')); ?>
            </div>
            <h1 class="dashboard-title">
                <i class="fas fa-chart-line"></i>
                ড্যাশবোর্ড
            </h1>
            <p class="dashboard-subtitle">
                স্বাগতম, <?php echo getCurrentUser()['full_name']; ?>! আপনার ব্যবসার সম্পূর্ণ চিত্র এক নজরে দেখুন
            </p>
        </div>

        <!-- Modern Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card" onclick="window.location.href='products/index.php'">
                <div class="stat-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="stat-number"><?php echo toBengaliNumber($stats['total_products']); ?></div>
                <div class="stat-label">মোট পণ্য</div>
                <div class="stat-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    <span>+৫% গত মাসের তুলনায়</span>
                </div>
            </div>

            <div class="stat-card info" onclick="window.location.href='customers/index.php'">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><?php echo toBengaliNumber($stats['total_customers']); ?></div>
                <div class="stat-label">মোট কাস্টমার</div>
                <div class="stat-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    <span>+৮% গত মাসের তুলনায়</span>
                </div>
            </div>

            <div class="stat-card info" onclick="window.location.href='suppliers/index.php'">
                <div class="stat-icon">
                    <i class="fas fa-industry"></i>
                </div>
                <div class="stat-number"><?php echo toBengaliNumber($stats['total_suppliers']); ?></div>
                <div class="stat-label">মোট সাপ্লায়ার</div>
                <div class="stat-trend trend-neutral">
                    <i class="fas fa-minus"></i>
                    <span>পূর্বের মতোই</span>
                </div>
            </div>

            <div class="stat-card success" onclick="window.location.href='sales/index.php'">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-number"><?php echo formatCurrency($stats['today_sales_amount']); ?></div>
                <div class="stat-label">আজকের বিক্রয়</div>
                <div class="stat-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    <span><?php echo toBengaliNumber($stats['today_sales_count']); ?> টি অর্ডার</span>
                </div>
            </div>

            <div class="stat-card success" onclick="window.location.href='reports/sales.php'">
                <div class="stat-icon">
                    <i class="fas fa-calendar-month"></i>
                </div>
                <div class="stat-number"><?php echo formatCurrency($stats['month_sales_amount']); ?></div>
                <div class="stat-label">এই মাসের বিক্রয়</div>
                <div class="stat-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    <span><?php echo toBengaliNumber($stats['month_sales_count']); ?> টি অর্ডার</span>
                </div>
            </div>

            <div class="stat-card warning" onclick="window.location.href='reports/low_stock.php'">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number"><?php echo toBengaliNumber($stats['low_stock_count']); ?></div>
                <div class="stat-label">কম স্টক পণ্য</div>
                <div class="stat-trend trend-down">
                    <i class="fas fa-arrow-down"></i>
                    <span>তাৎক্ষণিক মনোযোগ প্রয়োজন</span>
                </div>
            </div>

            <div class="stat-card danger" onclick="window.location.href='reports/expired.php'">
                <div class="stat-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-number"><?php echo toBengaliNumber($stats['expired_count']); ?></div>
                <div class="stat-label">মেয়াদ উত্তীর্ণ পণ্য</div>
                <div class="stat-trend trend-down">
                    <i class="fas fa-arrow-down"></i>
                    <span>অবিলম্বে অপসারণ করুন</span>
                </div>
            </div>

            <div class="stat-card warning" onclick="window.location.href='reports/expiring_soon.php'">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number"><?php echo toBengaliNumber($stats['expiring_soon_count']); ?></div>
                <div class="stat-label">শীঘ্রই মেয়াদ শেষ</div>
                <div class="stat-trend trend-neutral">
                    <i class="fas fa-clock"></i>
                    <span>নিয়মিত পর্যবেক্ষণ করুন</span>
                </div>
            </div>
        </div>

        <!-- Quick Actions Section -->
        <div class="dashboard-card">
            <div class="card-header-modern">
                <h3 class="card-title-modern">
                    <i class="fas fa-bolt"></i>
                    দ্রুত কাজ
                </h3>
                <div class="card-actions">
                    <button class="action-btn" title="রিফ্রেশ">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="action-btn" title="সেটিংস">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
            <div class="quick-actions-grid">
                <div class="quick-action-card" onclick="window.location.href='sales/add.php'">
                    <div class="quick-action-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="quick-action-title">নতুন বিক্রয়</div>
                    <div class="quick-action-desc">দ্রুত বিক্রয় এন্ট্রি করুন</div>
                </div>

                <div class="quick-action-card" onclick="window.location.href='purchases/add.php'">
                    <div class="quick-action-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="quick-action-title">নতুন ক্রয়</div>
                    <div class="quick-action-desc">পণ্য ক্রয় রেকর্ড করুন</div>
                </div>

                <div class="quick-action-card" onclick="window.location.href='products/add.php'">
                    <div class="quick-action-icon">
                        <i class="fas fa-box-open"></i>
                    </div>
                    <div class="quick-action-title">নতুন পণ্য</div>
                    <div class="quick-action-desc">ইনভেন্টরিতে পণ্য যোগ করুন</div>
                </div>

                <div class="quick-action-card" onclick="window.location.href='customers/add.php'">
                    <div class="quick-action-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="quick-action-title">নতুন কাস্টমার</div>
                    <div class="quick-action-desc">গ্রাহক তালিকায় যোগ করুন</div>
                </div>

                <div class="quick-action-card" onclick="window.location.href='reports/sales.php'">
                    <div class="quick-action-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="quick-action-title">বিক্রয় রিপোর্ট</div>
                    <div class="quick-action-desc">বিস্তারিত বিক্রয় বিশ্লেষণ</div>
                </div>

                <div class="quick-action-card" onclick="window.location.href='admin/backup.php'">
                    <div class="quick-action-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="quick-action-title">ব্যাকআপ</div>
                    <div class="quick-action-desc">ডেটা সুরক্ষিত রাখুন</div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="dashboard-card">
                    <div class="card-header-modern">
                        <h3 class="card-title-modern">
                            <i class="fas fa-chart-line"></i>
                            বিক্রয় ট্রেন্ড
                        </h3>
                        <div class="card-actions">
                            <button class="action-btn" title="ফুলস্ক্রিন">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="salesChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="dashboard-card">
                    <div class="card-header-modern">
                        <h3 class="card-title-modern">
                            <i class="fas fa-chart-pie"></i>
                            পণ্য ক্যাটাগরি
                        </h3>
                        <div class="card-actions">
                            <button class="action-btn" title="ফুলস্ক্রিন">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="categoryChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Sales -->
            <div class="col-md-8">
                <div class="dashboard-card">
                    <div class="card-header-modern">
                        <h3 class="card-title-modern">
                            <i class="fas fa-history"></i>
                            সাম্প্রতিক বিক্রয়
                        </h3>
                        <div class="card-actions">
                            <button class="action-btn" title="সব দেখুন" onclick="window.location.href='sales/index.php'">
                                <i class="fas fa-external-link-alt"></i>
                            </button>
                            <button class="action-btn" title="রিফ্রেশ">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <?php if (empty($recent_sales)): ?>
                        <div style="text-align: center; padding: 40px; color: #7f8c8d;">
                            <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                            <p style="font-size: 1.1rem; margin: 0;">কোন বিক্রয় তথ্য পাওয়া যায়নি।</p>
                            <p style="font-size: 0.9rem; margin-top: 10px;">নতুন বিক্রয় যোগ করতে উপরের কুইক অ্যাকশন ব্যবহার করুন।</p>
                        </div>
                    <?php else: ?>
                        <div class="modern-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-receipt"></i> ইনভয়েস নং</th>
                                        <th><i class="fas fa-user"></i> কাস্টমার</th>
                                        <th><i class="fas fa-calendar"></i> তারিখ</th>
                                        <th><i class="fas fa-money-bill-wave"></i> মোট টাকা</th>
                                        <th><i class="fas fa-info-circle"></i> স্ট্যাটাস</th>
                                        <th><i class="fas fa-cog"></i> অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_sales as $sale): ?>
                                        <tr>
                                            <td>
                                                <strong style="color: #667eea;"><?php echo $sale['invoice_no']; ?></strong>
                                            </td>
                                            <td>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div style="width: 35px; height: 35px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 14px;">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <span><?php echo $sale['customer_name'] ?? 'ওয়াক-ইন কাস্টমার'; ?></span>
                                                </div>
                                            </td>
                                            <td><?php echo formatDate($sale['sale_date']); ?></td>
                                            <td>
                                                <strong style="color: #27ae60; font-size: 1.1rem;">
                                                    <?php echo formatCurrency($sale['total_amount']); ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <?php if ($sale['status'] == 'completed'): ?>
                                                    <span class="status-badge status-success">সম্পন্ন</span>
                                                <?php elseif ($sale['status'] == 'pending'): ?>
                                                    <span class="status-badge status-warning">অপেক্ষমাণ</span>
                                                <?php else: ?>
                                                    <span class="status-badge status-danger">বাতিল</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div style="display: flex; gap: 5px;">
                                                    <button class="action-btn" style="width: 30px; height: 30px; font-size: 12px;" title="দেখুন">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="action-btn" style="width: 30px; height: 30px; font-size: 12px;" title="প্রিন্ট">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Alerts & Notifications -->
            <div class="col-md-4">
                <div class="dashboard-card">
                    <div class="card-header-modern">
                        <h3 class="card-title-modern">
                            <i class="fas fa-bell"></i>
                            সতর্কতা ও নোটিফিকেশন
                        </h3>
                        <div class="card-actions">
                            <button class="action-btn" title="সব পড়া হয়েছে চিহ্নিত করুন">
                                <i class="fas fa-check-double"></i>
                            </button>
                        </div>
                    </div>

                    <div class="alerts-container">
                        <?php if ($stats['low_stock_count'] > 0): ?>
                            <div class="modern-alert alert-warning">
                                <div class="alert-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="alert-content">
                                    <div class="alert-title">কম স্টক সতর্কতা</div>
                                    <div class="alert-message">
                                        <strong><?php echo toBengaliNumber($stats['low_stock_count']); ?>টি</strong> পণ্যের স্টক কম আছে
                                    </div>
                                    <a href="reports/low_stock.php" class="alert-action">বিস্তারিত দেখুন</a>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ($stats['expired_count'] > 0): ?>
                            <div class="modern-alert alert-danger">
                                <div class="alert-icon">
                                    <i class="fas fa-times-circle"></i>
                                </div>
                                <div class="alert-content">
                                    <div class="alert-title">মেয়াদ উত্তীর্ণ</div>
                                    <div class="alert-message">
                                        <strong><?php echo toBengaliNumber($stats['expired_count']); ?>টি</strong> পণ্যের মেয়াদ শেষ
                                    </div>
                                    <a href="reports/expired.php" class="alert-action">অবিলম্বে দেখুন</a>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ($stats['expiring_soon_count'] > 0): ?>
                            <div class="modern-alert alert-warning">
                                <div class="alert-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="alert-content">
                                    <div class="alert-title">শীঘ্রই মেয়াদ শেষ</div>
                                    <div class="alert-message">
                                        <strong><?php echo toBengaliNumber($stats['expiring_soon_count']); ?>টি</strong> পণ্যের মেয়াদ শীঘ্রই শেষ
                                    </div>
                                    <a href="reports/expiring_soon.php" class="alert-action">পর্যবেক্ষণ করুন</a>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ($stats['customer_dues'] > 0): ?>
                            <div class="modern-alert alert-info">
                                <div class="alert-icon">
                                    <i class="fas fa-user-clock"></i>
                                </div>
                                <div class="alert-content">
                                    <div class="alert-title">কাস্টমার বকেয়া</div>
                                    <div class="alert-message">
                                        মোট বকেয়া: <strong><?php echo formatCurrency($stats['customer_dues']); ?></strong>
                                    </div>
                                    <a href="reports/customer_dues.php" class="alert-action">সংগ্রহ করুন</a>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ($stats['supplier_dues'] > 0): ?>
                            <div class="modern-alert alert-info">
                                <div class="alert-icon">
                                    <i class="fas fa-industry"></i>
                                </div>
                                <div class="alert-content">
                                    <div class="alert-title">সাপ্লায়ার বকেয়া</div>
                                    <div class="alert-message">
                                        মোট বকেয়া: <strong><?php echo formatCurrency($stats['supplier_dues']); ?></strong>
                                    </div>
                                    <a href="reports/supplier_dues.php" class="alert-action">পরিশোধ করুন</a>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ($stats['low_stock_count'] == 0 && $stats['expired_count'] == 0 && $stats['expiring_soon_count'] == 0): ?>
                            <div class="modern-alert alert-success">
                                <div class="alert-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="alert-content">
                                    <div class="alert-title">সব কিছু ঠিক আছে!</div>
                                    <div class="alert-message">
                                        কোন জরুরি সতর্কতা নেই। আপনার ব্যবসা সুচারুভাবে চলছে।
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard JavaScript -->
<script>
$(document).ready(function() {
    // Initialize dashboard animations
    initDashboardAnimations();

    // Auto-refresh dashboard data every 5 minutes
    setInterval(refreshDashboardData, 300000);

    // Initialize real-time clock
    updateClock();
    setInterval(updateClock, 1000);

    // Initialize chart animations (if charts are added later)
    initChartAnimations();

    // Add click analytics
    trackDashboardInteractions();
});

function initDashboardAnimations() {
    // Animate stat cards on load
    $('.stat-card').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'translateY(30px)'
        });

        setTimeout(() => {
            $(this).animate({
                'opacity': '1'
            }, 600).css({
                'transform': 'translateY(0)',
                'transition': 'transform 0.6s ease'
            });
        }, index * 100);
    });

    // Animate alerts
    $('.modern-alert').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'translateX(-30px)'
        });

        setTimeout(() => {
            $(this).animate({
                'opacity': '1'
            }, 400).css({
                'transform': 'translateX(0)',
                'transition': 'transform 0.4s ease'
            });
        }, (index * 150) + 1000);
    });

    // Animate quick actions
    $('.quick-action-card').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'scale(0.8)'
        });

        setTimeout(() => {
            $(this).animate({
                'opacity': '1'
            }, 500).css({
                'transform': 'scale(1)',
                'transition': 'transform 0.5s ease'
            });
        }, (index * 100) + 500);
    });
}

function refreshDashboardData() {
    // Show loading indicator
    showNotification('ড্যাশবোর্ড ডেটা আপডেট করা হচ্ছে...', 'info', 2000);

    // Simulate data refresh (replace with actual AJAX call)
    setTimeout(function() {
        // Add pulse effect to updated cards
        $('.stat-number').addClass('pulse-update');
        setTimeout(function() {
            $('.stat-number').removeClass('pulse-update');
        }, 1000);

        showNotification('ড্যাশবোর্ড সফলভাবে আপডেট হয়েছে', 'success', 3000);
    }, 1500);
}

function updateClock() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('bn-BD', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    // Update time in dashboard header if element exists
    if ($('.dashboard-date').length) {
        const currentText = $('.dashboard-date').text();
        const dateOnly = currentText.split(' - ')[0];
        $('.dashboard-date').html(`<i class="fas fa-calendar-alt"></i> ${dateOnly} - <i class="fas fa-clock"></i> ${timeString}`);
    }
}

function initChartAnimations() {
    // Initialize Sales Chart
    const salesCtx = document.getElementById('salesChart');
    if (salesCtx) {
        const salesChart = new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: ['জানুয়ারি', 'ফেব্রুয়ারি', 'মার্চ', 'এপ্রিল', 'মে', 'জুন'],
                datasets: [{
                    label: 'বিক্রয় (টাকা)',
                    data: [12000, 19000, 15000, 25000, 22000, 30000],
                    borderColor: 'rgb(102, 126, 234)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgb(102, 126, 234)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            callback: function(value) {
                                return '৳' + value.toLocaleString();
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    // Initialize Category Chart
    const categoryCtx = document.getElementById('categoryChart');
    if (categoryCtx) {
        const categoryChart = new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: ['খাদ্য', 'পানীয়', 'ইলেকট্রনিক্স', 'পোশাক', 'অন্যান্য'],
                datasets: [{
                    data: [30, 20, 25, 15, 10],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(240, 147, 251, 0.8)',
                        'rgba(78, 205, 196, 0.8)',
                        'rgba(255, 107, 107, 0.8)'
                    ],
                    borderColor: [
                        'rgb(102, 126, 234)',
                        'rgb(118, 75, 162)',
                        'rgb(240, 147, 251)',
                        'rgb(78, 205, 196)',
                        'rgb(255, 107, 107)'
                    ],
                    borderWidth: 2,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 2000
                }
            }
        });
    }
}

function trackDashboardInteractions() {
    // Track stat card clicks
    $('.stat-card').on('click', function() {
        const cardType = $(this).find('.stat-label').text();
        console.log(`Stat card clicked: ${cardType}`);

        // Add click effect
        $(this).addClass('clicked');
        setTimeout(() => {
            $(this).removeClass('clicked');
        }, 200);
    });

    // Track quick action clicks
    $('.quick-action-card').on('click', function() {
        const actionType = $(this).find('.quick-action-title').text();
        console.log(`Quick action clicked: ${actionType}`);

        // Add click effect
        $(this).addClass('clicked');
        setTimeout(() => {
            $(this).removeClass('clicked');
        }, 200);
    });

    // Track alert interactions
    $('.alert-action').on('click', function() {
        const alertType = $(this).closest('.modern-alert').find('.alert-title').text();
        console.log(`Alert action clicked: ${alertType}`);
    });
}

// Add CSS for click effects
$('<style>')
    .prop('type', 'text/css')
    .html(`
        .pulse-update {
            animation: pulseUpdate 1s ease-in-out;
        }

        @keyframes pulseUpdate {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); color: #667eea; }
        }

        .clicked {
            transform: scale(0.95) !important;
            transition: transform 0.1s ease !important;
        }

        .stat-card:hover .stat-icon {
            animation: iconBounce 0.6s ease-in-out;
        }

        @keyframes iconBounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1.1) rotate(5deg); }
            40% { transform: translateY(-10px) scale(1.15) rotate(5deg); }
            60% { transform: translateY(-5px) scale(1.12) rotate(5deg); }
        }

        .dashboard-header::before {
            animation: dashboardGlow 6s ease-in-out infinite;
        }

        @keyframes dashboardGlow {
            0%, 100% { opacity: 0.1; transform: rotate(0deg) scale(1); }
            50% { opacity: 0.3; transform: rotate(180deg) scale(1.1); }
        }

        .welcome-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            z-index: 10000;
            max-width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .welcome-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: white;
            animation: welcomePulse 2s ease-in-out infinite;
        }

        @keyframes welcomePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .welcome-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .welcome-text {
            color: #7f8c8d;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .welcome-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .welcome-button:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
    `)
    .appendTo('head');

    // Show welcome message for first-time users
    if (localStorage.getItem('dashboardVisited') !== 'true') {
        showWelcomeMessage();
        localStorage.setItem('dashboardVisited', 'true');
    }

    // Initialize performance monitoring
    initPerformanceMonitoring();
}

function showWelcomeMessage() {
    const welcomeHtml = `
        <div class="welcome-message" id="welcomeMessage">
            <div class="welcome-icon">
                <i class="fas fa-rocket"></i>
            </div>
            <h3 class="welcome-title">স্বাগতম!</h3>
            <p class="welcome-text">
                আপনার নতুন আল্ট্রা মডার্ন ড্যাশবোর্ডে স্বাগতম।
                এখানে আপনি আপনার ব্যবসার সম্পূর্ণ চিত্র এক নজরে দেখতে পাবেন।
            </p>
            <button class="welcome-button" onclick="closeWelcomeMessage()">
                শুরু করুন
            </button>
        </div>
    `;

    $('body').append(welcomeHtml);

    setTimeout(() => {
        $('#welcomeMessage').css({
            'opacity': '0',
            'transform': 'translate(-50%, -50%) scale(0.8)'
        });

        setTimeout(() => {
            $('#welcomeMessage').css({
                'opacity': '1',
                'transform': 'translate(-50%, -50%) scale(1)',
                'transition': 'all 0.5s ease'
            });
        }, 100);
    }, 500);
}

function closeWelcomeMessage() {
    $('#welcomeMessage').css({
        'opacity': '0',
        'transform': 'translate(-50%, -50%) scale(0.8)',
        'transition': 'all 0.3s ease'
    });

    setTimeout(() => {
        $('#welcomeMessage').remove();
    }, 300);
}

function initPerformanceMonitoring() {
    const startTime = performance.now();

    $(window).on('load', function() {
        const loadTime = performance.now() - startTime;
        const performanceIndicator = `
            <div class="performance-indicator" id="performanceIndicator">
                <i class="fas fa-tachometer-alt"></i>
                লোড টাইম: ${Math.round(loadTime)}ms
            </div>
        `;

        $('body').append(performanceIndicator);

        setTimeout(() => {
            $('#performanceIndicator').addClass('show');
        }, 1000);

        setTimeout(() => {
            $('#performanceIndicator').removeClass('show');
        }, 5000);
    });
}
</script>

<?php include 'includes/footer.php'; ?>
