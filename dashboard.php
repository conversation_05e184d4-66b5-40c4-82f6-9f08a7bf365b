<?php
require_once 'config/config.php';
requireAuth();

$db = new Database();
$conn = $db->getConnection();

// Get dashboard statistics
$stats = [];

// Total products
$query = "SELECT COUNT(*) as count FROM products WHERE status = 'active'";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['total_products'] = $stmt->fetch()['count'];

// Total customers
$query = "SELECT COUNT(*) as count FROM customers WHERE status = 'active'";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['total_customers'] = $stmt->fetch()['count'];

// Total suppliers
$query = "SELECT COUNT(*) as count FROM suppliers WHERE status = 'active'";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['total_suppliers'] = $stmt->fetch()['count'];

// Today's sales
$query = "SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM sales WHERE DATE(created_at) = CURDATE()";
$stmt = $conn->prepare($query);
$stmt->execute();
$today_sales = $stmt->fetch();
$stats['today_sales_count'] = $today_sales['count'];
$stats['today_sales_amount'] = $today_sales['total'];

// This month's sales
$query = "SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM sales WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())";
$stmt = $conn->prepare($query);
$stmt->execute();
$month_sales = $stmt->fetch();
$stats['month_sales_count'] = $month_sales['count'];
$stats['month_sales_amount'] = $month_sales['total'];

// Low stock products
$low_stock_products = getLowStockProducts();
$stats['low_stock_count'] = count($low_stock_products);

// Expired products
$expired_products = getExpiredProducts();
$stats['expired_count'] = count($expired_products);

// Products expiring soon
$expiring_products = getExpiringSoonProducts();
$stats['expiring_soon_count'] = count($expiring_products);

// Recent sales
$query = "SELECT s.*, c.name as customer_name FROM sales s 
          LEFT JOIN customers c ON s.customer_id = c.id 
          ORDER BY s.created_at DESC LIMIT 10";
$stmt = $conn->prepare($query);
$stmt->execute();
$recent_sales = $stmt->fetchAll();

// Customer dues
$query = "SELECT SUM(balance) as total_due FROM customers WHERE balance > 0";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['customer_dues'] = $stmt->fetch()['total_due'] ?? 0;

// Supplier dues
$query = "SELECT SUM(balance) as total_due FROM suppliers WHERE balance > 0";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['supplier_dues'] = $stmt->fetch()['total_due'] ?? 0;

include 'includes/header.php';
?>

<div class="main-content">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>ড্যাশবোর্ড</h2>
            <div class="text-muted">
                আজকের তারিখ: <?php echo formatDate(date('Y-m-d')); ?>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo toBengaliNumber($stats['total_products']); ?></div>
                <div class="stat-label">মোট পণ্য</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo toBengaliNumber($stats['total_customers']); ?></div>
                <div class="stat-label">মোট কাস্টমার</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo toBengaliNumber($stats['total_suppliers']); ?></div>
                <div class="stat-label">মোট সাপ্লায়ার</div>
            </div>
            
            <div class="stat-card success">
                <div class="stat-number"><?php echo formatCurrency($stats['today_sales_amount']); ?></div>
                <div class="stat-label">আজকের বিক্রয় (<?php echo toBengaliNumber($stats['today_sales_count']); ?> টি)</div>
            </div>
            
            <div class="stat-card success">
                <div class="stat-number"><?php echo formatCurrency($stats['month_sales_amount']); ?></div>
                <div class="stat-label">এই মাসের বিক্রয় (<?php echo toBengaliNumber($stats['month_sales_count']); ?> টি)</div>
            </div>
            
            <div class="stat-card warning">
                <div class="stat-number"><?php echo toBengaliNumber($stats['low_stock_count']); ?></div>
                <div class="stat-label">কম স্টক পণ্য</div>
            </div>
            
            <div class="stat-card danger">
                <div class="stat-number"><?php echo toBengaliNumber($stats['expired_count']); ?></div>
                <div class="stat-label">মেয়াদ উত্তীর্ণ পণ্য</div>
            </div>
            
            <div class="stat-card warning">
                <div class="stat-number"><?php echo toBengaliNumber($stats['expiring_soon_count']); ?></div>
                <div class="stat-label">শীঘ্রই মেয়াদ শেষ</div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Sales -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>সাম্প্রতিক বিক্রয়</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_sales)): ?>
                            <p class="text-muted">কোন বিক্রয় তথ্য পাওয়া যায়নি।</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ইনভয়েস নং</th>
                                            <th>কাস্টমার</th>
                                            <th>তারিখ</th>
                                            <th>মোট টাকা</th>
                                            <th>স্ট্যাটাস</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_sales as $sale): ?>
                                            <tr>
                                                <td><?php echo $sale['invoice_no']; ?></td>
                                                <td><?php echo $sale['customer_name'] ?? 'ওয়াক-ইন কাস্টমার'; ?></td>
                                                <td><?php echo formatDate($sale['sale_date']); ?></td>
                                                <td><?php echo formatCurrency($sale['total_amount']); ?></td>
                                                <td>
                                                    <?php if ($sale['status'] == 'completed'): ?>
                                                        <span class="badge badge-success">সম্পন্ন</span>
                                                    <?php elseif ($sale['status'] == 'pending'): ?>
                                                        <span class="badge badge-warning">অপেক্ষমাণ</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger">বাতিল</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Quick Actions & Alerts -->
            <div class="col-md-4">
                <!-- Quick Actions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>দ্রুত কাজ</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="sales/add.php" class="btn btn-primary">নতুন বিক্রয়</a>
                            <a href="purchases/add.php" class="btn btn-success">নতুন ক্রয়</a>
                            <a href="products/add.php" class="btn btn-info">নতুন পণ্য</a>
                            <a href="customers/add.php" class="btn btn-secondary">নতুন কাস্টমার</a>
                        </div>
                    </div>
                </div>

                <!-- Alerts -->
                <div class="card">
                    <div class="card-header">
                        <h5>সতর্কতা</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($stats['low_stock_count'] > 0): ?>
                            <div class="alert alert-warning">
                                <strong><?php echo toBengaliNumber($stats['low_stock_count']); ?>টি</strong> পণ্যের স্টক কম আছে।
                                <a href="reports/low_stock.php" class="alert-link">দেখুন</a>
                            </div>
                        <?php endif; ?>

                        <?php if ($stats['expired_count'] > 0): ?>
                            <div class="alert alert-danger">
                                <strong><?php echo toBengaliNumber($stats['expired_count']); ?>টি</strong> পণ্যের মেয়াদ শেষ হয়ে গেছে।
                                <a href="reports/expired.php" class="alert-link">দেখুন</a>
                            </div>
                        <?php endif; ?>

                        <?php if ($stats['expiring_soon_count'] > 0): ?>
                            <div class="alert alert-warning">
                                <strong><?php echo toBengaliNumber($stats['expiring_soon_count']); ?>টি</strong> পণ্যের মেয়াদ শীঘ্রই শেষ হবে।
                                <a href="reports/expiring_soon.php" class="alert-link">দেখুন</a>
                            </div>
                        <?php endif; ?>

                        <?php if ($stats['customer_dues'] > 0): ?>
                            <div class="alert alert-info">
                                কাস্টমারদের মোট বকেয়া: <strong><?php echo formatCurrency($stats['customer_dues']); ?></strong>
                                <a href="reports/customer_dues.php" class="alert-link">দেখুন</a>
                            </div>
                        <?php endif; ?>

                        <?php if ($stats['supplier_dues'] > 0): ?>
                            <div class="alert alert-info">
                                সাপ্লায়ারদের মোট বকেয়া: <strong><?php echo formatCurrency($stats['supplier_dues']); ?></strong>
                                <a href="reports/supplier_dues.php" class="alert-link">দেখুন</a>
                            </div>
                        <?php endif; ?>

                        <?php if ($stats['low_stock_count'] == 0 && $stats['expired_count'] == 0 && $stats['expiring_soon_count'] == 0): ?>
                            <div class="alert alert-success">
                                সব কিছু ঠিক আছে! কোন সতর্কতা নেই।
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
