<?php
// Demo page to showcase the new menu design
require_once 'config/config.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$page_title = 'মেনু ডিজাইন ডেমো';

// Sample breadcrumbs
$breadcrumbs = [
    ['title' => 'ডেমো', 'icon' => 'fas fa-flask'],
    ['title' => 'মেনু ডিজাইন', 'icon' => 'fas fa-palette']
];

include 'includes/header.php';
?>

<div class="card">
    <div class="card-header">
        <h3><i class="fas fa-palette"></i> নতুন মেনু ডিজাইন ডেমো</h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h4>✨ নতুন ফিচারসমূহ:</h4>
                <ul class="feature-list">
                    <li><i class="fas fa-check text-success"></i> আধুনিক গ্রেডিয়েন্ট ডিজাইন</li>
                    <li><i class="fas fa-check text-success"></i> মোবাইল রেসপন্সিভ মেনু</li>
                    <li><i class="fas fa-check text-success"></i> সুন্দর হোভার ইফেক্ট</li>
                    <li><i class="fas fa-check text-success"></i> ড্রপডাউন অ্যানিমেশন</li>
                    <li><i class="fas fa-check text-success"></i> ব্রেডক্রাম্ব নেভিগেশন</li>
                    <li><i class="fas fa-check text-success"></i> স্ক্রোল টু টপ বাটন</li>
                    <li><i class="fas fa-check text-success"></i> উন্নত ফুটার ডিজাইন</li>
                    <li><i class="fas fa-check text-success"></i> লোডিং অ্যানিমেশন</li>
                    <li><i class="fas fa-check text-success"></i> নোটিফিকেশন সিস্টেম</li>
                    <li><i class="fas fa-check text-success"></i> কীবোর্ড নেভিগেশন</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h4>🎨 ডিজাইন উন্নতি:</h4>
                <ul class="feature-list">
                    <li><i class="fas fa-star text-warning"></i> সুন্দর রঙের সমন্বয়</li>
                    <li><i class="fas fa-star text-warning"></i> আইকন সহ মেনু আইটেম</li>
                    <li><i class="fas fa-star text-warning"></i> স্মুথ ট্রানজিশন</li>
                    <li><i class="fas fa-star text-warning"></i> ব্যাকড্রপ ব্লার ইফেক্ট</li>
                    <li><i class="fas fa-star text-warning"></i> শ্যাডো এবং গ্লো ইফেক্ট</li>
                    <li><i class="fas fa-star text-warning"></i> রেসপন্সিভ গ্রিড লেআউট</li>
                    <li><i class="fas fa-star text-warning"></i> অ্যাক্সেসিবিলিটি উন্নতি</li>
                    <li><i class="fas fa-star text-warning"></i> প্রিন্ট-ফ্রেন্ডলি স্টাইল</li>
                    <li><i class="fas fa-star text-warning"></i> ডার্ক মোড সাপোর্ট</li>
                    <li><i class="fas fa-star text-warning"></i> পারফরমেন্স অপটিমাইজেশন</li>
                </ul>
            </div>
        </div>
        
        <hr>
        
        <div class="demo-buttons">
            <h4>🧪 ডেমো ফাংশন:</h4>
            <div class="btn-group">
                <button class="btn btn-primary" onclick="showNotification('সফল! অপারেশন সম্পন্ন হয়েছে।', 'success')">
                    <i class="fas fa-check"></i> সাকসেস নোটিফিকেশন
                </button>
                <button class="btn btn-danger" onclick="showNotification('ত্রুটি! কিছু সমস্যা হয়েছে।', 'error')">
                    <i class="fas fa-times"></i> এরর নোটিফিকেশন
                </button>
                <button class="btn btn-warning" onclick="showNotification('সতর্কতা! এই অ্যাকশনটি সাবধানে করুন।', 'warning')">
                    <i class="fas fa-exclamation-triangle"></i> ওয়ার্নিং নোটিফিকেশন
                </button>
                <button class="btn btn-info" onclick="showNotification('তথ্য: নতুন আপডেট উপলব্ধ।', 'info')">
                    <i class="fas fa-info-circle"></i> ইনফো নোটিফিকেশন
                </button>
            </div>
            
            <div class="btn-group mt-3">
                <button class="btn btn-secondary" onclick="showPageLoading(); setTimeout(hidePageLoading, 3000)">
                    <i class="fas fa-spinner"></i> লোডিং টেস্ট
                </button>
                <button class="btn btn-primary" onclick="$('.navbar a').first().addClass('pulse')">
                    <i class="fas fa-heartbeat"></i> পালস ইফেক্ট
                </button>
                <button class="btn btn-success" onclick="addMenuBadge('.navbar li:first-child', 5)">
                    <i class="fas fa-bell"></i> মেনু ব্যাজ
                </button>
            </div>
        </div>
        
        <hr>
        
        <div class="instructions">
            <h4>📱 মোবাইল টেস্ট:</h4>
            <p>ব্রাউজারের ডেভেলপার টুলস খুলে মোবাইল ভিউ টেস্ট করুন। মেনু টগল বাটন এবং রেসপন্সিভ ডিজাইন দেখুন।</p>
            
            <h4>⌨️ কীবোর্ড শর্টকাট:</h4>
            <ul>
                <li><kbd>Ctrl + K</kbd> - মেনু সার্চ</li>
                <li><kbd>Esc</kbd> - মেনু বন্ধ করুন</li>
            </ul>
        </div>
    </div>
</div>

<style>
.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.feature-list li:last-child {
    border-bottom: none;
}

.feature-list i {
    margin-right: 10px;
    width: 20px;
}

.demo-buttons {
    text-align: center;
}

.btn-group {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.instructions {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.instructions h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.instructions ul {
    margin-bottom: 0;
}

kbd {
    background: #333;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-group .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>

<script>
// Demo specific JavaScript
$(document).ready(function() {
    // Show welcome notification
    setTimeout(function() {
        showNotification('স্বাগতম! নতুন মেনু ডিজাইন দেখুন।', 'info', 8000);
    }, 1000);
    
    // Auto-remove pulse effect after 5 seconds
    setTimeout(function() {
        $('.navbar a').removeClass('pulse');
    }, 5000);
});
</script>

<?php include 'includes/footer.php'; ?>
