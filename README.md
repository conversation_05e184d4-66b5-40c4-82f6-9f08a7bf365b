# দোকান ম্যানেজমেন্ট সিস্টেম
## Shop Management System

একটি সম্পূর্ণ দোকান ব্যবস্থাপনা সিস্টেম যা PHP, MySQL, HTML, jQuery, AJAX, JavaScript এবং CSS ব্যবহার করে তৈরি।

## বৈশিষ্ট্য (Features)

### ✅ সম্পন্ন মডিউল
- **ড্যাশবোর্ড**: সামগ্রিক পরিসংখ্যান এবং দ্রুত অ্যাক্সেস
- **পণ্য ব্যবস্থাপনা**: পণ্য যোগ, সম্পাদনা, স্টক ট্র্যাকিং
- **বিক্রয় ব্যবস্থাপনা**: বিক্রয<PERSON> এন্ট্রি, ইনভয়েস জেনারেশন
- **রিপোর্ট**: বিক<PERSON><PERSON><PERSON><PERSON> রিপোর্ট, স্টক রিপোর্ট
- **ইউজার অথেনটিকেশন**: লগইন/লগআউট সিস্টেম

### 🚧 আসন্ন মডিউল
- **কাস্টমার ব্যবস্থাপনা**: কাস্টমার তথ্য, বকেয়া হিসাব
- **সাপ<PERSON><PERSON><PERSON>য়ার ব্যবস্থাপনা**: সা<PERSON><PERSON><PERSON><PERSON><PERSON>়ার তথ্য, ক্রয় হিসাব
- **ক্রয় ব্যবস্থাপনা**: ক্রয় এন্ট্রি, সাপ্লায়ার পেমেন্ট
- **এক্সপেন্স ট্র্যাকিং**: খরচের হিসাব
- **ড্যামেজ/লস ট্র্যাকিং**: নষ্ট পণ্যের হিসাব
- **হালখাতা চিঠি জেনারেশন**: বার্ষিক হালখাতার চিঠি
- **অ্যাডভান্স রিপোর্ট**: লাভ-ক্ষতি, মাসিক/বার্ষিক রিপোর্ট

## প্রয়োজনীয়তা (Requirements)

- PHP 7.4 বা তার উপরে
- MySQL 5.7 বা তার উপরে
- Apache/Nginx ওয়েব সার্ভার
- XAMPP/WAMP/LAMP (লোকাল ডেভেলপমেন্টের জন্য)

## ইনস্টলেশন (Installation)

### ১. প্রজেক্ট ডাউনলোড
```bash
# প্রজেক্ট ফোল্ডার XAMPP এর htdocs এ কপি করুন
# উদাহরণ: C:\xampp\htdocs\dokan\
```

### ২. ডাটাবেস সেটআপ
1. phpMyAdmin খুলুন (http://localhost/phpmyadmin)
2. নতুন ডাটাবেস তৈরি করুন: `shop_management`
3. `database/shop_management.sql` ফাইল ইমপোর্ট করুন

### ৩. কনফিগারেশন
`config/database.php` ফাইলে ডাটাবেস সেটিংস চেক করুন:
```php
private $host = 'localhost';
private $db_name = 'shop_management';
private $username = 'root';
private $password = '';
```

### ৪. অ্যাক্সেস
ব্রাউজারে যান: `http://localhost/dokan`

## ডিফল্ট লগইন

- **ইউজারনেম**: admin
- **পাসওয়ার্ড**: password

## ফোল্ডার স্ট্রাকচার

```
dokan/
├── config/                 # কনফিগারেশন ফাইল
│   ├── config.php
│   └── database.php
├── includes/               # সাধারণ ফাইল
│   ├── functions.php
│   ├── auth.php
│   ├── header.php
│   └── footer.php
├── assets/                 # CSS, JS, Images
│   ├── css/
│   ├── js/
│   └── images/
├── database/               # ডাটাবেস ফাইল
│   └── shop_management.sql
├── products/               # পণ্য ব্যবস্থাপনা
├── sales/                  # বিক্রয় ব্যবস্থাপনা
├── purchases/              # ক্রয় ব্যবস্থাপনা
├── customers/              # কাস্টমার ব্যবস্থাপনা
├── suppliers/              # সাপ্লায়ার ব্যবস্থাপনা
├── reports/                # রিপোর্ট
├── ajax/                   # AJAX ফাইল
├── admin/                  # অ্যাডমিন প্যানেল
├── dashboard.php           # মূল ড্যাশবোর্ড
├── login.php               # লগইন পেজ
├── logout.php              # লগআউট
└── index.php               # হোম পেজ
```

## ব্যবহার (Usage)

### পণ্য যোগ করা
1. ড্যাশবোর্ড থেকে "পণ্য ব্যবস্থাপনা" > "নতুন পণ্য" এ যান
2. পণ্যের তথ্য পূরণ করুন
3. "পণ্য সংরক্ষণ করুন" বাটনে ক্লিক করুন

### বিক্রয় করা
1. "বিক্রয়" > "নতুন বিক্রয়" এ যান
2. কাস্টমার নির্বাচন করুন (ঐচ্ছিক)
3. পণ্য যোগ করুন
4. পেমেন্ট তথ্য দিন
5. "বিক্রয় সম্পন্ন করুন" বাটনে ক্লিক করুন

### রিপোর্ট দেখা
1. "রিপোর্ট" মেনু থেকে প্রয়োজনীয় রিপোর্ট নির্বাচন করুন
2. তারিখ ফিল্টার সেট করুন
3. "রিপোর্ট দেখুন" বাটনে ক্লিক করুন

## বৈশিষ্ট্য বিবরণ

### ড্যাশবোর্ড
- মোট পণ্য, কাস্টমার, সাপ্লায়ার সংখ্যা
- আজকের ও মাসিক বিক্রয়
- কম স্টক ও মেয়াদ উত্তীর্ণ পণ্যের সতর্কতা
- সাম্প্রতিক বিক্রয় তালিকা

### পণ্য ব্যবস্থাপনা
- পণ্য যোগ/সম্পাদনা/মুছে ফেলা
- ক্যাটাগরি ভিত্তিক সংগঠন
- স্টক ট্র্যাকিং
- মেয়াদ উত্তীর্ণ ট্র্যাকিং
- বারকোড সাপোর্ট

### বিক্রয় ব্যবস্থাপনা
- দ্রুত পণ্য সার্চ
- অটো ক্যালকুলেশন
- ইনভয়েস জেনারেশন
- পেমেন্ট ট্র্যাকিং

## নিরাপত্তা

- পাসওয়ার্ড হ্যাশিং
- SQL ইনজেকশন প্রতিরোধ
- XSS প্রতিরোধ
- সেশন ব্যবস্থাপনা
- ইউজার রোল ভিত্তিক অ্যাক্সেস

## সাপোর্ট

সমস্যার জন্য GitHub Issues ব্যবহার করুন।

## লাইসেন্স

এই প্রজেক্ট MIT লাইসেন্সের অধীনে।

---

**নোট**: এটি একটি ডেভেলপমেন্ট ভার্সন। প্রোডাকশনে ব্যবহারের আগে অতিরিক্ত নিরাপত্তা ব্যবস্থা নিন।
