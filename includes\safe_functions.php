<?php
// Safe Functions - No Conflicts
// This file contains only unique functions with conflict checking

// Check if toBengaliNumber exists, if not create it
if (!function_exists('toBengaliNumber')) {
    function toBengaliNumber($number) {
        $bengali_digits = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
        $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        return str_replace($english_digits, $bengali_digits, (string)$number);
    }
}

// Check if formatDate exists, if not create it
if (!function_exists('formatDate')) {
    function formatDate($date, $format = 'd/m/Y') {
        if (is_string($date)) {
            $date = new DateTime($date);
        }
        $formatted = $date->format($format);
        return toBengaliNumber($formatted);
    }
}

// Check if formatCurrency exists, if not create it
if (!function_exists('formatCurrency')) {
    function formatCurrency($amount, $symbol = '৳') {
        $formatted = number_format($amount, 2);
        return $symbol . ' ' . toBengaliNumber($formatted);
    }
}

// Check if getBengaliMonth exists, if not create it
if (!function_exists('getBengaliMonth')) {
    function getBengaliMonth($month_number) {
        $bengali_months = [
            1 => 'জানুয়ারি',
            2 => 'ফেব্রুয়ারি', 
            3 => 'মার্চ',
            4 => 'এপ্রিল',
            5 => 'মে',
            6 => 'জুন',
            7 => 'জুলাই',
            8 => 'আগস্ট',
            9 => 'সেপ্টেম্বর',
            10 => 'অক্টোবর',
            11 => 'নভেম্বর',
            12 => 'ডিসেম্বর'
        ];
        return $bengali_months[$month_number] ?? '';
    }
}

// Check if getBengaliDay exists, if not create it
if (!function_exists('getBengaliDay')) {
    function getBengaliDay($day_number) {
        $bengali_days = [
            0 => 'রবিবার',
            1 => 'সোমবার',
            2 => 'মঙ্গলবার',
            3 => 'বুধবার',
            4 => 'বৃহস্পতিবার',
            5 => 'শুক্রবার',
            6 => 'শনিবার'
        ];
        return $bengali_days[$day_number] ?? '';
    }
}

// Check if formatFullBengaliDate exists, if not create it
if (!function_exists('formatFullBengaliDate')) {
    function formatFullBengaliDate($date = null) {
        if ($date === null) {
            $date = new DateTime();
        } elseif (is_string($date)) {
            $date = new DateTime($date);
        }
        
        $day = $date->format('w');
        $day_name = getBengaliDay($day);
        $day_number = toBengaliNumber($date->format('d'));
        $month = getBengaliMonth($date->format('n'));
        $year = toBengaliNumber($date->format('Y'));
        
        return "{$day_name}, {$day_number} {$month} {$year}";
    }
}

// Check if timeAgoBengali exists, if not create it
if (!function_exists('timeAgoBengali')) {
    function timeAgoBengali($datetime) {
        $time = time() - strtotime($datetime);
        
        if ($time < 60) {
            return toBengaliNumber($time) . ' সেকেন্ড আগে';
        } elseif ($time < 3600) {
            $minutes = floor($time / 60);
            return toBengaliNumber($minutes) . ' মিনিট আগে';
        } elseif ($time < 86400) {
            $hours = floor($time / 3600);
            return toBengaliNumber($hours) . ' ঘন্টা আগে';
        } elseif ($time < 2592000) {
            $days = floor($time / 86400);
            return toBengaliNumber($days) . ' দিন আগে';
        } elseif ($time < 31536000) {
            $months = floor($time / 2592000);
            return toBengaliNumber($months) . ' মাস আগে';
        } else {
            $years = floor($time / 31536000);
            return toBengaliNumber($years) . ' বছর আগে';
        }
    }
}

// Check if sanitizeBengaliInput exists, if not create it
if (!function_exists('sanitizeBengaliInput')) {
    function sanitizeBengaliInput($input) {
        $input = trim($input);
        $input = stripslashes($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        return $input;
    }
}

// Check if generateSlug exists, if not create it
if (!function_exists('generateSlug')) {
    function generateSlug($text) {
        // Convert Bengali to English transliteration (basic)
        $bengali_to_english = [
            'ক' => 'k', 'খ' => 'kh', 'গ' => 'g', 'ঘ' => 'gh', 'ঙ' => 'ng',
            'চ' => 'ch', 'ছ' => 'chh', 'জ' => 'j', 'ঝ' => 'jh', 'ঞ' => 'ny',
            'ট' => 't', 'ঠ' => 'th', 'ড' => 'd', 'ঢ' => 'dh', 'ণ' => 'n',
            'ত' => 't', 'থ' => 'th', 'দ' => 'd', 'ধ' => 'dh', 'ন' => 'n',
            'প' => 'p', 'ফ' => 'ph', 'ব' => 'b', 'ভ' => 'bh', 'ম' => 'm',
            'য' => 'y', 'র' => 'r', 'ল' => 'l', 'শ' => 'sh', 'ষ' => 'sh',
            'স' => 's', 'হ' => 'h', 'া' => 'a', 'ি' => 'i', 'ী' => 'i',
            'ু' => 'u', 'ূ' => 'u', 'ে' => 'e', 'ৈ' => 'oi', 'ো' => 'o',
            'ৌ' => 'ou', 'ং' => 'ng', 'ঃ' => 'h', '্' => '', ' ' => '-'
        ];
        
        $slug = strtr($text, $bengali_to_english);
        $slug = strtolower($slug);
        $slug = preg_replace('/[^a-z0-9-]/', '', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');
        
        return $slug;
    }
}

// Check if formatFileSize exists, if not create it
if (!function_exists('formatFileSize')) {
    function formatFileSize($bytes) {
        $units = ['বাইট', 'কেবি', 'এমবি', 'জিবি', 'টিবি'];
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return toBengaliNumber(round($bytes, 2)) . ' ' . $units[$i];
    }
}

// Check if getStatusBadge exists, if not create it
if (!function_exists('getStatusBadge')) {
    function getStatusBadge($status, $active_text = 'সক্রিয়', $inactive_text = 'নিষ্ক্রিয়') {
        if ($status === 'active' || $status === 1 || $status === true) {
            return '<span class="badge badge-success"><i class="fas fa-check-circle"></i> ' . $active_text . '</span>';
        } else {
            return '<span class="badge badge-danger"><i class="fas fa-times-circle"></i> ' . $inactive_text . '</span>';
        }
    }
}

// Check if validateBengaliPhone exists, if not create it
if (!function_exists('validateBengaliPhone')) {
    function validateBengaliPhone($phone) {
        // Remove Bengali numbers and convert to English
        $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        $bengali_digits = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
        
        $phone = str_replace($bengali_digits, $english_digits, $phone);
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Bangladesh phone number validation
        if (preg_match('/^(?:\+88|88)?01[3-9]\d{8}$/', $phone)) {
            return $phone;
        }
        
        return false;
    }
}

// Check if generateQRCode exists, if not create it
if (!function_exists('generateQRCode')) {
    function generateQRCode($data, $size = 200) {
        $encoded_data = urlencode($data);
        return "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data={$encoded_data}";
    }
}
?>
