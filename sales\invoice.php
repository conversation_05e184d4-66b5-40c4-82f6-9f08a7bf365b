<?php
require_once '../config/config.php';
requireAuth();

$page_title = 'বিক্রয় ইনভয়েস';

if (!isset($_GET['id']) || empty($_GET['id'])) {
    showAlert('ভুল ইনভয়েস আইডি।', 'error');
    redirect('index.php');
}

$sale_id = (int)$_GET['id'];

$db = new Database();
$conn = $db->getConnection();

// Get sale information
$query = "SELECT s.*, c.name as customer_name, c.phone as customer_phone, c.address as customer_address, 
          u.full_name as created_by_name 
          FROM sales s 
          LEFT JOIN customers c ON s.customer_id = c.id 
          LEFT JOIN users u ON s.created_by = u.id 
          WHERE s.id = :sale_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':sale_id', $sale_id);
$stmt->execute();
$sale = $stmt->fetch();

if (!$sale) {
    showAlert('বিক্রয় তথ্য পাওয়া যায়নি।', 'error');
    redirect('index.php');
}

// Get sale details
$query = "SELECT sd.*, p.name as product_name, p.unit 
          FROM sale_details sd 
          JOIN products p ON sd.product_id = p.id 
          WHERE sd.sale_id = :sale_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':sale_id', $sale_id);
$stmt->execute();
$sale_details = $stmt->fetchAll();

include '../includes/header.php';
?>

<div class="main-content">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-file-invoice"></i> বিক্রয় ইনভয়েস</h2>
            <div>
                <button onclick="window.print();" class="btn btn-primary">
                    <i class="fas fa-print"></i> প্রিন্ট
                </button>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-list"></i> বিক্রয় তালিকা
                </a>
            </div>
        </div>

        <!-- Invoice -->
        <div class="card" id="invoice">
            <div class="card-body">
                <!-- Invoice Header -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h2 class="mb-0"><?php echo APP_NAME; ?></h2>
                        <p>
                            ঠিকানা: ১২৩, মেইন রোড, ঢাকা<br>
                            ফোন: ০১৭১২৩৪৫৬৭৮<br>
                            ইমেইল: <EMAIL>
                        </p>
                    </div>
                    <div class="col-md-6 text-right">
                        <h1 class="mb-0">ইনভয়েস</h1>
                        <p>
                            ইনভয়েস নং: <strong><?php echo $sale['invoice_no']; ?></strong><br>
                            তারিখ: <strong><?php echo formatDate($sale['sale_date']); ?></strong><br>
                            স্ট্যাটাস: 
                            <?php if ($sale['status'] == 'completed'): ?>
                                <span class="badge badge-success">সম্পন্ন</span>
                            <?php elseif ($sale['status'] == 'pending'): ?>
                                <span class="badge badge-warning">অপেক্ষমাণ</span>
                            <?php else: ?>
                                <span class="badge badge-danger">বাতিল</span>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>

                <!-- Customer Info -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>কাস্টমার তথ্য</h5>
                        <p>
                            <?php if ($sale['customer_id']): ?>
                                নাম: <strong><?php echo $sale['customer_name']; ?></strong><br>
                                <?php if ($sale['customer_phone']): ?>
                                    ফোন: <?php echo $sale['customer_phone']; ?><br>
                                <?php endif; ?>
                                <?php if ($sale['customer_address']): ?>
                                    ঠিকানা: <?php echo $sale['customer_address']; ?>
                                <?php endif; ?>
                            <?php else: ?>
                                <strong>ওয়াক-ইন কাস্টমার</strong>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="col-md-6 text-right">
                        <h5>বিক্রয়কারী</h5>
                        <p>
                            নাম: <strong><?php echo $sale['created_by_name']; ?></strong><br>
                            তারিখ: <?php echo formatDateTime($sale['created_at']); ?>
                        </p>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="table-responsive mb-4">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>পণ্য</th>
                                <th>পরিমাণ</th>
                                <th>একক মূল্য</th>
                                <th>মোট</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $subtotal = 0;
                            foreach ($sale_details as $index => $item): 
                                $subtotal += $item['total_price'];
                            ?>
                                <tr>
                                    <td><?php echo toBengaliNumber($index + 1); ?></td>
                                    <td><?php echo $item['product_name']; ?></td>
                                    <td><?php echo toBengaliNumber($item['quantity']) . ' ' . $item['unit']; ?></td>
                                    <td class="text-right"><?php echo formatCurrency($item['unit_price']); ?></td>
                                    <td class="text-right"><?php echo formatCurrency($item['total_price']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="4" class="text-right"><strong>সাবটোটাল:</strong></td>
                                <td class="text-right"><?php echo formatCurrency($subtotal); ?></td>
                            </tr>
                            <?php if ($sale['discount'] > 0): ?>
                            <tr>
                                <td colspan="4" class="text-right"><strong>ছাড়:</strong></td>
                                <td class="text-right"><?php echo formatCurrency($sale['discount']); ?></td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <td colspan="4" class="text-right"><strong>মোট:</strong></td>
                                <td class="text-right"><strong><?php echo formatCurrency($sale['total_amount']); ?></strong></td>
                            </tr>
                            <tr>
                                <td colspan="4" class="text-right"><strong>প্রদত্ত:</strong></td>
                                <td class="text-right"><?php echo formatCurrency($sale['paid_amount']); ?></td>
                            </tr>
                            <tr>
                                <td colspan="4" class="text-right"><strong>বকেয়া:</strong></td>
                                <td class="text-right"><?php echo formatCurrency($sale['due_amount']); ?></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!-- Notes -->
                <?php if (!empty($sale['notes'])): ?>
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5>মন্তব্য</h5>
                        <p><?php echo nl2br($sale['notes']); ?></p>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Footer -->
                <div class="row">
                    <div class="col-md-12 text-center">
                        <p class="mb-0">আমাদের দোকানে কেনাকাটা করার জন্য ধন্যবাদ!</p>
                        <p class="mb-0">ইনভয়েস তৈরি করা হয়েছে: <?php echo formatDateTime($sale['created_at']); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    body * {
        visibility: hidden;
    }
    #invoice, #invoice * {
        visibility: visible;
    }
    #invoice {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
    .card {
        border: none !important;
    }
    .card-body {
        padding: 0 !important;
    }
    .main-content {
        padding: 0 !important;
        margin: 0 !important;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
